<!DOCTYPE html><html><head>

  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Exchek is a comprehensive B2B and B2C financial application designed to simplify cross-border transactions and currency exchange.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="exchek">
  <link rel="apple-touch-icon" sizes="180x180" href="icons/apple-touch-icon.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="icons/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="icons/favicon-16x16.png">
  <link rel="shortcut icon" type="image/x-icon" href="icons/favicon.ico">

  <title>exchek</title>
  <link rel="manifest" href="manifest.json">
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
</head>
<body>
  <script src='https://cdn.jsdelivr.net/npm/pdfjs-dist@4.6.82/build/pdf.min.mjs' type='module'></script>
  <script type='module'>
  var { pdfjsLib } = globalThis;
  pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.6.82/build/pdf.worker.mjs';

  var pdfRenderOptions = {
    cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.6.82/cmaps/',
    cMapPacked: true,
  }
  </script>
  <script src="main.dart.js" type="application/javascript"></script>
</body></html>