// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in exchek/test/views/account_setup_view_test/business_account_setup_view_test/business_account_kyc_setup_view_test/pan_detail_view_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:exchek/core/utils/exports.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeBusinessAccountSetupState_0 extends _i1.SmartFake
    implements _i2.BusinessAccountSetupState {
  _FakeBusinessAccountSetupState_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [BusinessAccountSetupBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockBusinessAccountSetupBloc extends _i1.Mock
    implements _i2.BusinessAccountSetupBloc {
  MockBusinessAccountSetupBloc() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.BusinessAccountSetupState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeBusinessAccountSetupState_0(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i2.BusinessAccountSetupState);

  @override
  _i3.Stream<_i2.BusinessAccountSetupState> get stream =>
      (super.noSuchMethod(
            Invocation.getter(#stream),
            returnValue: _i3.Stream<_i2.BusinessAccountSetupState>.empty(),
          )
          as _i3.Stream<_i2.BusinessAccountSetupState>);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  _i3.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i2.FileData?> getFileDataFromPath(
    String? path,
    String? fallbackName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getFileDataFromPath, [path, fallbackName]),
            returnValue: _i3.Future<_i2.FileData?>.value(),
          )
          as _i3.Future<_i2.FileData?>);

  @override
  _i3.Future<_i2.FileData?> downloadFileDataFromUrl(
    String? url,
    String? name,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#downloadFileDataFromUrl, [url, name]),
            returnValue: _i3.Future<_i2.FileData?>.value(),
          )
          as _i3.Future<_i2.FileData?>);

  @override
  void add(_i2.BusinessAccountSetupEvent? event) => super.noSuchMethod(
    Invocation.method(#add, [event]),
    returnValueForMissingStub: null,
  );

  @override
  void onEvent(_i2.BusinessAccountSetupEvent? event) => super.noSuchMethod(
    Invocation.method(#onEvent, [event]),
    returnValueForMissingStub: null,
  );

  @override
  void emit(_i2.BusinessAccountSetupState? state) => super.noSuchMethod(
    Invocation.method(#emit, [state]),
    returnValueForMissingStub: null,
  );

  @override
  void on<E extends _i2.BusinessAccountSetupEvent>(
    _i2.EventHandler<E, _i2.BusinessAccountSetupState>? handler, {
    _i2.EventTransformer<E>? transformer,
  }) => super.noSuchMethod(
    Invocation.method(#on, [handler], {#transformer: transformer}),
    returnValueForMissingStub: null,
  );

  @override
  void onTransition(
    _i2.Transition<
      _i2.BusinessAccountSetupEvent,
      _i2.BusinessAccountSetupState
    >?
    transition,
  ) => super.noSuchMethod(
    Invocation.method(#onTransition, [transition]),
    returnValueForMissingStub: null,
  );

  @override
  void onChange(_i2.Change<_i2.BusinessAccountSetupState>? change) =>
      super.noSuchMethod(
        Invocation.method(#onChange, [change]),
        returnValueForMissingStub: null,
      );

  @override
  void addError(Object? error, [StackTrace? stackTrace]) => super.noSuchMethod(
    Invocation.method(#addError, [error, stackTrace]),
    returnValueForMissingStub: null,
  );

  @override
  void onError(Object? error, StackTrace? stackTrace) => super.noSuchMethod(
    Invocation.method(#onError, [error, stackTrace]),
    returnValueForMissingStub: null,
  );
}
