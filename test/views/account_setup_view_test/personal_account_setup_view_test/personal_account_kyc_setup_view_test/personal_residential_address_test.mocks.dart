// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in exchek/test/views/account_setup_view_test/personal_account_setup_view_test/personal_account_kyc_setup_view_test/personal_residential_address_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:exchek/core/utils/exports.dart' as _i4;
import 'package:exchek/viewmodels/account_setup_bloc/personal_account_setup_bloc/personal_account_setup_bloc.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakePersonalAccountSetupState_0 extends _i1.SmartFake
    implements _i2.PersonalAccountSetupState {
  _FakePersonalAccountSetupState_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [PersonalAccountSetupBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockPersonalAccountSetupBloc extends _i1.Mock
    implements _i2.PersonalAccountSetupBloc {
  MockPersonalAccountSetupBloc() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.PersonalAccountSetupState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakePersonalAccountSetupState_0(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i2.PersonalAccountSetupState);

  @override
  _i3.Stream<_i2.PersonalAccountSetupState> get stream =>
      (super.noSuchMethod(
            Invocation.getter(#stream),
            returnValue: _i3.Stream<_i2.PersonalAccountSetupState>.empty(),
          )
          as _i3.Stream<_i2.PersonalAccountSetupState>);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  _i3.Future<_i4.FileData?> getFileDataFromPath(
    String? path,
    String? fallbackName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getFileDataFromPath, [path, fallbackName]),
            returnValue: _i3.Future<_i4.FileData?>.value(),
          )
          as _i3.Future<_i4.FileData?>);

  @override
  _i3.Future<_i4.FileData?> downloadFileDataFromUrl(
    String? url,
    String? name,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#downloadFileDataFromUrl, [url, name]),
            returnValue: _i3.Future<_i4.FileData?>.value(),
          )
          as _i3.Future<_i4.FileData?>);

  @override
  _i3.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  void add(_i2.PersonalAccountSetupEvent? event) => super.noSuchMethod(
    Invocation.method(#add, [event]),
    returnValueForMissingStub: null,
  );

  @override
  void onEvent(_i2.PersonalAccountSetupEvent? event) => super.noSuchMethod(
    Invocation.method(#onEvent, [event]),
    returnValueForMissingStub: null,
  );

  @override
  void emit(_i2.PersonalAccountSetupState? state) => super.noSuchMethod(
    Invocation.method(#emit, [state]),
    returnValueForMissingStub: null,
  );

  @override
  void on<E extends _i2.PersonalAccountSetupEvent>(
    _i4.EventHandler<E, _i2.PersonalAccountSetupState>? handler, {
    _i4.EventTransformer<E>? transformer,
  }) => super.noSuchMethod(
    Invocation.method(#on, [handler], {#transformer: transformer}),
    returnValueForMissingStub: null,
  );

  @override
  void onTransition(
    _i4.Transition<
      _i2.PersonalAccountSetupEvent,
      _i2.PersonalAccountSetupState
    >?
    transition,
  ) => super.noSuchMethod(
    Invocation.method(#onTransition, [transition]),
    returnValueForMissingStub: null,
  );

  @override
  void onChange(_i4.Change<_i2.PersonalAccountSetupState>? change) =>
      super.noSuchMethod(
        Invocation.method(#onChange, [change]),
        returnValueForMissingStub: null,
      );

  @override
  void addError(Object? error, [StackTrace? stackTrace]) => super.noSuchMethod(
    Invocation.method(#addError, [error, stackTrace]),
    returnValueForMissingStub: null,
  );

  @override
  void onError(Object? error, StackTrace? stackTrace) => super.noSuchMethod(
    Invocation.method(#onError, [error, stackTrace]),
    returnValueForMissingStub: null,
  );
}
