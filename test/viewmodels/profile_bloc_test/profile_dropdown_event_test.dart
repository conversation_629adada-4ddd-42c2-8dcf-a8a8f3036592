import 'package:exchek/viewmodels/profile_bloc/profile_dropdown_event.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ProfileDropdownEvent', () {
    group('ToggleDropdownEvent', () {
      test('supports value equality', () {
        final event1 = ToggleDropdownEvent();
        final event2 = ToggleDropdownEvent();

        expect(event1, equals(event2));
      });

      test('props are empty', () {
        final event = ToggleDropdownEvent();
        expect(event.props, []);
      });

      test('extends ProfileDropdownEvent', () {
        final event = ToggleDropdownEvent();
        expect(event, isA<ProfileDropdownEvent>());
      });
    });

    group('CloseDropdownEvent', () {
      test('supports value equality', () {
        final event1 = CloseDropdownEvent();
        final event2 = CloseDropdownEvent();

        expect(event1, equals(event2));
      });

      test('props are empty', () {
        final event = CloseDropdownEvent();
        expect(event.props, []);
      });

      test('extends ProfileDropdownEvent', () {
        final event = CloseDropdownEvent();
        expect(event, isA<ProfileDropdownEvent>());
      });
    });

    group('LogoutRequestedEvent', () {
      test('supports value equality', () {
        final event1 = LogoutRequestedEvent();
        final event2 = LogoutRequestedEvent();

        expect(event1, equals(event2));
      });

      test('props are empty', () {
        final event = LogoutRequestedEvent();
        expect(event.props, []);
      });

      test('extends ProfileDropdownEvent', () {
        final event = LogoutRequestedEvent();
        expect(event, isA<ProfileDropdownEvent>());
      });
    });

    group('LogoutConfirmedEvent', () {
      test('supports value equality', () {
        final event1 = LogoutConfirmedEvent();
        final event2 = LogoutConfirmedEvent();

        expect(event1, equals(event2));
      });

      test('props are empty', () {
        final event = LogoutConfirmedEvent();
        expect(event.props, []);
      });

      test('extends ProfileDropdownEvent', () {
        final event = LogoutConfirmedEvent();
        expect(event, isA<ProfileDropdownEvent>());
      });
    });

    group('LogoutWithEmailRequested', () {
      test('supports value equality', () {
        const event1 = LogoutWithEmailRequested('<EMAIL>');
        const event2 = LogoutWithEmailRequested('<EMAIL>');
        const event3 = LogoutWithEmailRequested('<EMAIL>');

        expect(event1, equals(event2));
        expect(event1, isNot(equals(event3)));
      });

      test('props include email', () {
        const event = LogoutWithEmailRequested('<EMAIL>');
        expect(event.props, ['<EMAIL>']);
      });

      test('extends ProfileDropdownEvent', () {
        const event = LogoutWithEmailRequested('<EMAIL>');
        expect(event, isA<ProfileDropdownEvent>());
      });

      test('email property is accessible', () {
        const email = '<EMAIL>';
        const event = LogoutWithEmailRequested(email);
        expect(event.email, equals(email));
      });

      test('different emails create different events', () {
        const event1 = LogoutWithEmailRequested('<EMAIL>');
        const event2 = LogoutWithEmailRequested('<EMAIL>');

        expect(event1, isNot(equals(event2)));
        expect(event1.props, isNot(equals(event2.props)));
      });

      test('empty email is handled correctly', () {
        const event1 = LogoutWithEmailRequested('');
        const event2 = LogoutWithEmailRequested('');

        expect(event1, equals(event2));
        expect(event1.props, ['']);
      });
    });

    group('ProfileDropdownEvent base class', () {
      test('all events extend ProfileDropdownEvent', () {
        final events = [
          ToggleDropdownEvent(),
          CloseDropdownEvent(),
          LogoutRequestedEvent(),
          LogoutConfirmedEvent(),
          const LogoutWithEmailRequested('<EMAIL>'),
        ];

        for (final event in events) {
          expect(event, isA<ProfileDropdownEvent>());
        }
      });
    });
  });
}
