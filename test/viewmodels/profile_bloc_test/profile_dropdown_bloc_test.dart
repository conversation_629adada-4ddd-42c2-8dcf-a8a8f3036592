import 'package:bloc_test/bloc_test.dart';
import 'package:exchek/viewmodels/profile_bloc/profile_dropdown_bloc.dart';
import 'package:exchek/viewmodels/profile_bloc/profile_dropdown_event.dart';
import 'package:exchek/viewmodels/profile_bloc/profile_dropdown_state.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:exchek/core/api_config/client/api_client.dart';
import 'package:exchek/core/oauth2/oauth2_config.dart';
import 'package:exchek/repository/auth_repository.dart';
import 'package:exchek/models/auth_models/common_success_model.dart';
import 'package:flutter/services.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

class MockApiClient extends Mock implements ApiClient {}

class MockOAuth2Config extends Mock implements OAuth2Config {}

class FakeApiClient extends Fake implements ApiClient {}

class FakeOAuth2Config extends Fake implements OAuth2Config {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  // Mock the flutter_secure_storage plugin to prevent MissingPluginException
  TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
    const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
    (MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'read':
          return null; // Return null for any read operation
        case 'write':
          return null; // Return null for any write operation
        case 'delete':
          return null; // Return null for any delete operation
        case 'deleteAll':
          return null; // Return null for any deleteAll operation
        case 'readAll':
          return <String, String>{}; // Return empty map for readAll
        default:
          return null;
      }
    },
  );

  group('ProfileDropdownBloc', () {
    late ProfileDropdownBloc bloc;
    late MockAuthRepository mockAuthRepository;

    setUpAll(() {
      registerFallbackValue(FakeApiClient());
      registerFallbackValue(FakeOAuth2Config());
    });

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      bloc = ProfileDropdownBloc();
    });

    test('initial state is ProfileDropdownInitial', () {
      expect(bloc.state, isA<ProfileDropdownInitial>());
    });

    blocTest<ProfileDropdownBloc, ProfileDropdownState>(
      'emits [ProfileDropdownOpen] when ToggleDropdownEvent is added from initial',
      build: () => ProfileDropdownBloc(),
      act: (bloc) => bloc.add(ToggleDropdownEvent()),
      expect: () => [isA<ProfileDropdownOpen>()],
    );

    blocTest<ProfileDropdownBloc, ProfileDropdownState>(
      'emits [ProfileDropdownClosed] when ToggleDropdownEvent is added from open',
      build: () => ProfileDropdownBloc(),
      seed: () => ProfileDropdownOpen(),
      act: (bloc) => bloc.add(ToggleDropdownEvent()),
      expect: () => [isA<ProfileDropdownClosed>()],
    );

    blocTest<ProfileDropdownBloc, ProfileDropdownState>(
      'emits [ProfileDropdownClosed] when CloseDropdownEvent is added',
      build: () => ProfileDropdownBloc(),
      act: (bloc) => bloc.add(CloseDropdownEvent()),
      expect: () => [isA<ProfileDropdownClosed>()],
    );

    blocTest<ProfileDropdownBloc, ProfileDropdownState>(
      'emits [ProfileDropdownClosed] when LogoutRequestedEvent is added',
      build: () => ProfileDropdownBloc(),
      act: (bloc) => bloc.add(LogoutRequestedEvent()),
      expect: () => [isA<ProfileDropdownClosed>()],
    );

    blocTest<ProfileDropdownBloc, ProfileDropdownState>(
      'emits [ProfileDropdownLoggingOut] when LogoutConfirmedEvent is added',
      build: () => ProfileDropdownBloc(),
      act: (bloc) => bloc.add(LogoutConfirmedEvent()),
      expect: () => [isA<ProfileDropdownLoggingOut>()],
    );

    group('LogoutWithEmailRequested', () {
      const email = '<EMAIL>';
      final event = LogoutWithEmailRequested(email);
      final successResponse = CommonSuccessModel(success: true);
      final failureResponse = CommonSuccessModel(success: false, message: 'Logout failed');

      setUp(() {
        registerFallbackValue(event);
      });

      blocTest<ProfileDropdownBloc, ProfileDropdownState>(
        'emits [ProfileDropdownLoggingOut, ProfileDropdownLogoutSuccess] on successful logout',
        build: () {
          when(() => mockAuthRepository.logout(email: email)).thenAnswer((_) async => successResponse);
          return ProfileDropdownBloc(authRepository: mockAuthRepository);
        },
        act: (bloc) => bloc.add(event),
        wait: const Duration(milliseconds: 100),
        expect: () => [isA<ProfileDropdownLoggingOut>(), isA<ProfileDropdownLogoutSuccess>()],
      );

      blocTest<ProfileDropdownBloc, ProfileDropdownState>(
        'emits [ProfileDropdownLoggingOut, ProfileDropdownLogoutFailure] on failed logout',
        build: () {
          when(() => mockAuthRepository.logout(email: email)).thenAnswer((_) async => failureResponse);
          return ProfileDropdownBloc(authRepository: mockAuthRepository);
        },
        act: (bloc) => bloc.add(event),
        wait: const Duration(milliseconds: 100),
        expect: () => [isA<ProfileDropdownLoggingOut>(), isA<ProfileDropdownLogoutFailure>()],
      );

      blocTest<ProfileDropdownBloc, ProfileDropdownState>(
        'emits [ProfileDropdownLoggingOut, ProfileDropdownLogoutFailure] on exception',
        build: () {
          when(() => mockAuthRepository.logout(email: email)).thenThrow(Exception('Network error'));
          return ProfileDropdownBloc(authRepository: mockAuthRepository);
        },
        act: (bloc) => bloc.add(event),
        wait: const Duration(milliseconds: 100),
        expect: () => [isA<ProfileDropdownLoggingOut>(), isA<ProfileDropdownLogoutFailure>()],
      );

      blocTest<ProfileDropdownBloc, ProfileDropdownState>(
        'emits [ProfileDropdownLoggingOut, ProfileDropdownLogoutFailure] when response success is null',
        build: () {
          final nullSuccessResponse = CommonSuccessModel(success: null, message: 'Unknown error');
          when(() => mockAuthRepository.logout(email: email)).thenAnswer((_) async => nullSuccessResponse);
          return ProfileDropdownBloc(authRepository: mockAuthRepository);
        },
        act: (bloc) => bloc.add(event),
        wait: const Duration(milliseconds: 100),
        expect: () => [isA<ProfileDropdownLoggingOut>(), isA<ProfileDropdownLogoutFailure>()],
      );

      blocTest<ProfileDropdownBloc, ProfileDropdownState>(
        'emits [ProfileDropdownLoggingOut, ProfileDropdownLogoutFailure] with custom message on failed logout',
        build: () {
          final customFailureResponse = CommonSuccessModel(success: false, message: 'Custom error message');
          when(() => mockAuthRepository.logout(email: email)).thenAnswer((_) async => customFailureResponse);
          return ProfileDropdownBloc(authRepository: mockAuthRepository);
        },
        act: (bloc) => bloc.add(event),
        wait: const Duration(milliseconds: 100),
        expect: () => [isA<ProfileDropdownLoggingOut>(), isA<ProfileDropdownLogoutFailure>()],
        verify: (bloc) {
          final state = bloc.state as ProfileDropdownLogoutFailure;
          expect(state.message, equals('Custom error message'));
        },
      );
    });

    group('Multiple events', () {
      blocTest<ProfileDropdownBloc, ProfileDropdownState>(
        'handles multiple toggle events correctly',
        build: () => ProfileDropdownBloc(),
        act: (bloc) {
          bloc.add(ToggleDropdownEvent()); // Should open
          bloc.add(ToggleDropdownEvent()); // Should close
          bloc.add(ToggleDropdownEvent()); // Should open again
        },
        expect: () => [isA<ProfileDropdownOpen>(), isA<ProfileDropdownClosed>(), isA<ProfileDropdownOpen>()],
      );

      blocTest<ProfileDropdownBloc, ProfileDropdownState>(
        'close event works from any state',
        build: () => ProfileDropdownBloc(),
        act: (bloc) {
          bloc.add(ToggleDropdownEvent()); // Open first
          bloc.add(CloseDropdownEvent()); // Then close
        },
        expect: () => [isA<ProfileDropdownOpen>(), isA<ProfileDropdownClosed>()],
      );

      blocTest<ProfileDropdownBloc, ProfileDropdownState>(
        'logout requested closes dropdown',
        build: () => ProfileDropdownBloc(),
        seed: () => ProfileDropdownOpen(),
        act: (bloc) => bloc.add(LogoutRequestedEvent()),
        expect: () => [isA<ProfileDropdownClosed>()],
      );
    });

    group('State verification', () {
      test('ProfileDropdownLogoutFailure contains correct message', () {
        const message = 'Test error message';
        final state = ProfileDropdownLogoutFailure(message);
        expect(state.message, equals(message));
        expect(state.props, contains(message));
      });

      test('all states extend ProfileDropdownState', () {
        expect(ProfileDropdownInitial(), isA<ProfileDropdownState>());
        expect(ProfileDropdownOpen(), isA<ProfileDropdownState>());
        expect(ProfileDropdownClosed(), isA<ProfileDropdownState>());
        expect(ProfileDropdownLoggingOut(), isA<ProfileDropdownState>());
        expect(ProfileDropdownLogoutSuccess(), isA<ProfileDropdownState>());
        expect(ProfileDropdownLogoutFailure('test'), isA<ProfileDropdownState>());
      });

      test('events have correct props', () {
        const email = '<EMAIL>';
        final event = LogoutWithEmailRequested(email);
        expect(event.props, contains(email));
        expect(event.email, equals(email));
      });
    });
  });
}
