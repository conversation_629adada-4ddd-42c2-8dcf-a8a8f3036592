// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in exchek/test/viewmodels/account_setup_bloc_test/personal_account_setup_bloc_test/personal_account_setup_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i23;
import 'dart:ui' as _i24;

import 'package:camera/camera.dart' as _i13;
import 'package:camera_platform_interface/camera_platform_interface.dart'
    as _i12;
import 'package:exchek/core/utils/exports.dart' as _i2;
import 'package:exchek/models/auth_models/common_success_model.dart' as _i4;
import 'package:exchek/models/auth_models/email_availabilty_model.dart' as _i6;
import 'package:exchek/models/auth_models/get_user_detail_model.dart' as _i11;
import 'package:exchek/models/auth_models/login_email_register_model.dart'
    as _i9;
import 'package:exchek/models/auth_models/mobile_availabilty_model.dart' as _i7;
import 'package:exchek/models/auth_models/validate_login_otp_model.dart' as _i5;
import 'package:exchek/models/auth_models/verify_email_model.dart' as _i3;
import 'package:exchek/models/personal_user_models/aadhar_otp_model.dart'
    as _i16;
import 'package:exchek/models/personal_user_models/aadhar_verify_otp_model.dart'
    as _i17;
import 'package:exchek/models/personal_user_models/bank_account_verify_model.dart'
    as _i21;
import 'package:exchek/models/personal_user_models/captcha_model.dart' as _i14;
import 'package:exchek/models/personal_user_models/get_city_and_state_model.dart'
    as _i19;
import 'package:exchek/models/personal_user_models/get_currency_model.dart'
    as _i10;
import 'package:exchek/models/personal_user_models/get_gst_details_model.dart'
    as _i20;
import 'package:exchek/models/personal_user_models/get_option_model.dart'
    as _i8;
import 'package:exchek/models/personal_user_models/get_pan_detail_model.dart'
    as _i18;
import 'package:exchek/models/personal_user_models/presigned_url_model.dart'
    as _i22;
import 'package:exchek/models/personal_user_models/recaptcha_model.dart'
    as _i15;
import 'package:exchek/repository/personal_user_kyc_repository.dart' as _i25;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeApiClient_0 extends _i1.SmartFake implements _i2.ApiClient {
  _FakeApiClient_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeOAuth2Config_1 extends _i1.SmartFake implements _i2.OAuth2Config {
  _FakeOAuth2Config_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeVerifyEmailModel_2 extends _i1.SmartFake
    implements _i3.VerifyEmailModel {
  _FakeVerifyEmailModel_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCommonSuccessModel_3 extends _i1.SmartFake
    implements _i4.CommonSuccessModel {
  _FakeCommonSuccessModel_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeValidateLoginOtpModel_4 extends _i1.SmartFake
    implements _i5.ValidateLoginOtpModel {
  _FakeValidateLoginOtpModel_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEmailAvailabilityModel_5 extends _i1.SmartFake
    implements _i6.EmailAvailabilityModel {
  _FakeEmailAvailabilityModel_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMobileAvailabilityModel_6 extends _i1.SmartFake
    implements _i7.MobileAvailabilityModel {
  _FakeMobileAvailabilityModel_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetDropdownOptionModel_7 extends _i1.SmartFake
    implements _i8.GetDropdownOptionModel {
  _FakeGetDropdownOptionModel_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLoginEmailPasswordModel_8 extends _i1.SmartFake
    implements _i9.LoginEmailPasswordModel {
  _FakeLoginEmailPasswordModel_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetCurrencyOptionModel_9 extends _i1.SmartFake
    implements _i10.GetCurrencyOptionModel {
  _FakeGetCurrencyOptionModel_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetUserDetailModel_10 extends _i1.SmartFake
    implements _i11.GetUserDetailModel {
  _FakeGetUserDetailModel_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMediaSettings_11 extends _i1.SmartFake
    implements _i12.MediaSettings {
  _FakeMediaSettings_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCameraDescription_12 extends _i1.SmartFake
    implements _i12.CameraDescription {
  _FakeCameraDescription_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCameraValue_13 extends _i1.SmartFake implements _i13.CameraValue {
  _FakeCameraValue_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeXFile_14 extends _i1.SmartFake implements _i12.XFile {
  _FakeXFile_14(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeWidget_15 extends _i1.SmartFake implements _i2.Widget {
  _FakeWidget_15(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);

  @override
  String toString({_i2.DiagnosticLevel? minLevel = _i2.DiagnosticLevel.info}) =>
      super.toString();
}

class _FakeCaptchaModel_16 extends _i1.SmartFake implements _i14.CaptchaModel {
  _FakeCaptchaModel_16(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRecaptchaModel_17 extends _i1.SmartFake
    implements _i15.RecaptchaModel {
  _FakeRecaptchaModel_17(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAadharOTPSendModel_18 extends _i1.SmartFake
    implements _i16.AadharOTPSendModel {
  _FakeAadharOTPSendModel_18(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAadharOTPVerifyModel_19 extends _i1.SmartFake
    implements _i17.AadharOTPVerifyModel {
  _FakeAadharOTPVerifyModel_19(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetPanDetailModel_20 extends _i1.SmartFake
    implements _i18.GetPanDetailModel {
  _FakeGetPanDetailModel_20(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetCityAndStateModel_21 extends _i1.SmartFake
    implements _i19.GetCityAndStateModel {
  _FakeGetCityAndStateModel_21(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetGstDetailsModel_22 extends _i1.SmartFake
    implements _i20.GetGstDetailsModel {
  _FakeGetGstDetailsModel_22(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBankAccountVerifyModel_23 extends _i1.SmartFake
    implements _i21.BankAccountVerifyModel {
  _FakeBankAccountVerifyModel_23(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePresignedUrlModel_24 extends _i1.SmartFake
    implements _i22.PresignedUrlModel {
  _FakePresignedUrlModel_24(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i2.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeApiClient_0(this, Invocation.getter(#apiClient)),
          )
          as _i2.ApiClient);

  @override
  _i2.OAuth2Config get oauth2Config =>
      (super.noSuchMethod(
            Invocation.getter(#oauth2Config),
            returnValue: _FakeOAuth2Config_1(
              this,
              Invocation.getter(#oauth2Config),
            ),
          )
          as _i2.OAuth2Config);

  @override
  _i23.Future<_i3.VerifyEmailModel> sendEmailVerificationLink({
    required String? email,
    required String? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerificationLink, [], {
              #email: email,
              #type: type,
            }),
            returnValue: _i23.Future<_i3.VerifyEmailModel>.value(
              _FakeVerifyEmailModel_2(
                this,
                Invocation.method(#sendEmailVerificationLink, [], {
                  #email: email,
                  #type: type,
                }),
              ),
            ),
          )
          as _i23.Future<_i3.VerifyEmailModel>);

  @override
  _i23.Future<_i4.CommonSuccessModel> resetPasswordVerificationLink({
    required String? email,
    required String? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#resetPasswordVerificationLink, [], {
              #email: email,
              #type: type,
            }),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#resetPasswordVerificationLink, [], {
                  #email: email,
                  #type: type,
                }),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i4.CommonSuccessModel> sendOtp({
    required String? mobile,
    required String? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendOtp, [], {#mobile: mobile, #type: type}),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#sendOtp, [], {#mobile: mobile, #type: type}),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i5.ValidateLoginOtpModel> validateLoginOtp({
    required String? mobile,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateLoginOtp, [], {
              #mobile: mobile,
              #otp: otp,
            }),
            returnValue: _i23.Future<_i5.ValidateLoginOtpModel>.value(
              _FakeValidateLoginOtpModel_4(
                this,
                Invocation.method(#validateLoginOtp, [], {
                  #mobile: mobile,
                  #otp: otp,
                }),
              ),
            ),
          )
          as _i23.Future<_i5.ValidateLoginOtpModel>);

  @override
  _i23.Future<_i4.CommonSuccessModel> validateregistrationOtp({
    required String? mobile,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateregistrationOtp, [], {
              #mobile: mobile,
              #otp: otp,
            }),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#validateregistrationOtp, [], {
                  #mobile: mobile,
                  #otp: otp,
                }),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i4.CommonSuccessModel> validateForgotPasswordOtp({
    required String? mobile,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateForgotPasswordOtp, [], {
              #mobile: mobile,
              #otp: otp,
            }),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#validateForgotPasswordOtp, [], {
                  #mobile: mobile,
                  #otp: otp,
                }),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i4.CommonSuccessModel> updatePassword({
    required String? confirmpassword,
    required String? email,
    required String? mobilenumber,
    required String? newpassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #confirmpassword: confirmpassword,
              #email: email,
              #mobilenumber: mobilenumber,
              #newpassword: newpassword,
            }),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#updatePassword, [], {
                  #confirmpassword: confirmpassword,
                  #email: email,
                  #mobilenumber: mobilenumber,
                  #newpassword: newpassword,
                }),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i6.EmailAvailabilityModel> emailAvailability({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#emailAvailability, [], {#email: email}),
            returnValue: _i23.Future<_i6.EmailAvailabilityModel>.value(
              _FakeEmailAvailabilityModel_5(
                this,
                Invocation.method(#emailAvailability, [], {#email: email}),
              ),
            ),
          )
          as _i23.Future<_i6.EmailAvailabilityModel>);

  @override
  _i23.Future<_i7.MobileAvailabilityModel> mobileAvailability({
    required String? mobileNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#mobileAvailability, [], {
              #mobileNumber: mobileNumber,
            }),
            returnValue: _i23.Future<_i7.MobileAvailabilityModel>.value(
              _FakeMobileAvailabilityModel_6(
                this,
                Invocation.method(#mobileAvailability, [], {
                  #mobileNumber: mobileNumber,
                }),
              ),
            ),
          )
          as _i23.Future<_i7.MobileAvailabilityModel>);

  @override
  _i23.Future<_i8.GetDropdownOptionModel> getDropdownOptions() =>
      (super.noSuchMethod(
            Invocation.method(#getDropdownOptions, []),
            returnValue: _i23.Future<_i8.GetDropdownOptionModel>.value(
              _FakeGetDropdownOptionModel_7(
                this,
                Invocation.method(#getDropdownOptions, []),
              ),
            ),
          )
          as _i23.Future<_i8.GetDropdownOptionModel>);

  @override
  _i23.Future<_i9.LoginEmailPasswordModel> loginuser({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loginuser, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i23.Future<_i9.LoginEmailPasswordModel>.value(
              _FakeLoginEmailPasswordModel_8(
                this,
                Invocation.method(#loginuser, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i23.Future<_i9.LoginEmailPasswordModel>);

  @override
  _i23.Future<_i9.LoginEmailPasswordModel> registerPersonalUser({
    required String? email,
    required String? estimatedMonthlyVolume,
    required String? mobileNumber,
    required List<String>? multicurrency,
    required String? receivingreason,
    required List<String>? profession,
    required String? productDescription,
    required String? legalFullName,
    required String? password,
    required Map<dynamic, dynamic>? tosacceptance,
    required String? usertype,
    required String? website,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerPersonalUser, [], {
              #email: email,
              #estimatedMonthlyVolume: estimatedMonthlyVolume,
              #mobileNumber: mobileNumber,
              #multicurrency: multicurrency,
              #receivingreason: receivingreason,
              #profession: profession,
              #productDescription: productDescription,
              #legalFullName: legalFullName,
              #password: password,
              #tosacceptance: tosacceptance,
              #usertype: usertype,
              #website: website,
            }),
            returnValue: _i23.Future<_i9.LoginEmailPasswordModel>.value(
              _FakeLoginEmailPasswordModel_8(
                this,
                Invocation.method(#registerPersonalUser, [], {
                  #email: email,
                  #estimatedMonthlyVolume: estimatedMonthlyVolume,
                  #mobileNumber: mobileNumber,
                  #multicurrency: multicurrency,
                  #receivingreason: receivingreason,
                  #profession: profession,
                  #productDescription: productDescription,
                  #legalFullName: legalFullName,
                  #password: password,
                  #tosacceptance: tosacceptance,
                  #usertype: usertype,
                  #website: website,
                }),
              ),
            ),
          )
          as _i23.Future<_i9.LoginEmailPasswordModel>);

  @override
  _i23.Future<_i9.LoginEmailPasswordModel> registerBusinessUser({
    required String? email,
    required String? estimatedMonthlyVolume,
    required String? mobileNumber,
    required List<String>? multicurrency,
    required String? businesstype,
    required String? businessnature,
    required List<String>? exportstype,
    required String? businesslegalname,
    required String? password,
    required Map<dynamic, dynamic>? tosacceptance,
    required String? usertype,
    required String? username,
    required String? website,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerBusinessUser, [], {
              #email: email,
              #estimatedMonthlyVolume: estimatedMonthlyVolume,
              #mobileNumber: mobileNumber,
              #multicurrency: multicurrency,
              #businesstype: businesstype,
              #businessnature: businessnature,
              #exportstype: exportstype,
              #businesslegalname: businesslegalname,
              #password: password,
              #tosacceptance: tosacceptance,
              #usertype: usertype,
              #username: username,
              #website: website,
            }),
            returnValue: _i23.Future<_i9.LoginEmailPasswordModel>.value(
              _FakeLoginEmailPasswordModel_8(
                this,
                Invocation.method(#registerBusinessUser, [], {
                  #email: email,
                  #estimatedMonthlyVolume: estimatedMonthlyVolume,
                  #mobileNumber: mobileNumber,
                  #multicurrency: multicurrency,
                  #businesstype: businesstype,
                  #businessnature: businessnature,
                  #exportstype: exportstype,
                  #businesslegalname: businesslegalname,
                  #password: password,
                  #tosacceptance: tosacceptance,
                  #usertype: usertype,
                  #username: username,
                  #website: website,
                }),
              ),
            ),
          )
          as _i23.Future<_i9.LoginEmailPasswordModel>);

  @override
  _i23.Future<_i10.GetCurrencyOptionModel> getCurrencyOptions() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrencyOptions, []),
            returnValue: _i23.Future<_i10.GetCurrencyOptionModel>.value(
              _FakeGetCurrencyOptionModel_9(
                this,
                Invocation.method(#getCurrencyOptions, []),
              ),
            ),
          )
          as _i23.Future<_i10.GetCurrencyOptionModel>);

  @override
  _i23.Future<_i4.CommonSuccessModel> logout({required String? email}) =>
      (super.noSuchMethod(
            Invocation.method(#logout, [], {#email: email}),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#logout, [], {#email: email}),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i11.GetUserDetailModel> getUserDetails({
    required String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserDetails, [], {#userId: userId}),
            returnValue: _i23.Future<_i11.GetUserDetailModel>.value(
              _FakeGetUserDetailModel_10(
                this,
                Invocation.method(#getUserDetails, [], {#userId: userId}),
              ),
            ),
          )
          as _i23.Future<_i11.GetUserDetailModel>);
}

/// A class which mocks [CameraController].
///
/// See the documentation for Mockito's code generation for more information.
class MockCameraController extends _i1.Mock implements _i13.CameraController {
  MockCameraController() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i12.MediaSettings get mediaSettings =>
      (super.noSuchMethod(
            Invocation.getter(#mediaSettings),
            returnValue: _FakeMediaSettings_11(
              this,
              Invocation.getter(#mediaSettings),
            ),
          )
          as _i12.MediaSettings);

  @override
  _i12.CameraDescription get description =>
      (super.noSuchMethod(
            Invocation.getter(#description),
            returnValue: _FakeCameraDescription_12(
              this,
              Invocation.getter(#description),
            ),
          )
          as _i12.CameraDescription);

  @override
  _i12.ResolutionPreset get resolutionPreset =>
      (super.noSuchMethod(
            Invocation.getter(#resolutionPreset),
            returnValue: _i12.ResolutionPreset.low,
          )
          as _i12.ResolutionPreset);

  @override
  bool get enableAudio =>
      (super.noSuchMethod(Invocation.getter(#enableAudio), returnValue: false)
          as bool);

  @override
  int get cameraId =>
      (super.noSuchMethod(Invocation.getter(#cameraId), returnValue: 0) as int);

  @override
  _i13.CameraValue get value =>
      (super.noSuchMethod(
            Invocation.getter(#value),
            returnValue: _FakeCameraValue_13(this, Invocation.getter(#value)),
          )
          as _i13.CameraValue);

  @override
  set value(_i13.CameraValue? newValue) => super.noSuchMethod(
    Invocation.setter(#value, newValue),
    returnValueForMissingStub: null,
  );

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  void debugCheckIsDisposed() => super.noSuchMethod(
    Invocation.method(#debugCheckIsDisposed, []),
    returnValueForMissingStub: null,
  );

  @override
  _i23.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> prepareForVideoRecording() =>
      (super.noSuchMethod(
            Invocation.method(#prepareForVideoRecording, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> pausePreview() =>
      (super.noSuchMethod(
            Invocation.method(#pausePreview, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> resumePreview() =>
      (super.noSuchMethod(
            Invocation.method(#resumePreview, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> setDescription(_i12.CameraDescription? description) =>
      (super.noSuchMethod(
            Invocation.method(#setDescription, [description]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i12.XFile> takePicture() =>
      (super.noSuchMethod(
            Invocation.method(#takePicture, []),
            returnValue: _i23.Future<_i12.XFile>.value(
              _FakeXFile_14(this, Invocation.method(#takePicture, [])),
            ),
          )
          as _i23.Future<_i12.XFile>);

  @override
  _i23.Future<void> startImageStream(
    _i13.onLatestImageAvailable? onAvailable,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startImageStream, [onAvailable]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> stopImageStream() =>
      (super.noSuchMethod(
            Invocation.method(#stopImageStream, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> startVideoRecording({
    _i13.onLatestImageAvailable? onAvailable,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#startVideoRecording, [], {
              #onAvailable: onAvailable,
            }),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<_i12.XFile> stopVideoRecording() =>
      (super.noSuchMethod(
            Invocation.method(#stopVideoRecording, []),
            returnValue: _i23.Future<_i12.XFile>.value(
              _FakeXFile_14(this, Invocation.method(#stopVideoRecording, [])),
            ),
          )
          as _i23.Future<_i12.XFile>);

  @override
  _i23.Future<void> pauseVideoRecording() =>
      (super.noSuchMethod(
            Invocation.method(#pauseVideoRecording, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> resumeVideoRecording() =>
      (super.noSuchMethod(
            Invocation.method(#resumeVideoRecording, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i2.Widget buildPreview() =>
      (super.noSuchMethod(
            Invocation.method(#buildPreview, []),
            returnValue: _FakeWidget_15(
              this,
              Invocation.method(#buildPreview, []),
            ),
          )
          as _i2.Widget);

  @override
  _i23.Future<double> getMaxZoomLevel() =>
      (super.noSuchMethod(
            Invocation.method(#getMaxZoomLevel, []),
            returnValue: _i23.Future<double>.value(0.0),
          )
          as _i23.Future<double>);

  @override
  _i23.Future<double> getMinZoomLevel() =>
      (super.noSuchMethod(
            Invocation.method(#getMinZoomLevel, []),
            returnValue: _i23.Future<double>.value(0.0),
          )
          as _i23.Future<double>);

  @override
  _i23.Future<void> setZoomLevel(double? zoom) =>
      (super.noSuchMethod(
            Invocation.method(#setZoomLevel, [zoom]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> setFlashMode(_i12.FlashMode? mode) =>
      (super.noSuchMethod(
            Invocation.method(#setFlashMode, [mode]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> setExposureMode(_i12.ExposureMode? mode) =>
      (super.noSuchMethod(
            Invocation.method(#setExposureMode, [mode]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> setExposurePoint(_i24.Offset? point) =>
      (super.noSuchMethod(
            Invocation.method(#setExposurePoint, [point]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<double> getMinExposureOffset() =>
      (super.noSuchMethod(
            Invocation.method(#getMinExposureOffset, []),
            returnValue: _i23.Future<double>.value(0.0),
          )
          as _i23.Future<double>);

  @override
  _i23.Future<double> getMaxExposureOffset() =>
      (super.noSuchMethod(
            Invocation.method(#getMaxExposureOffset, []),
            returnValue: _i23.Future<double>.value(0.0),
          )
          as _i23.Future<double>);

  @override
  _i23.Future<double> getExposureOffsetStepSize() =>
      (super.noSuchMethod(
            Invocation.method(#getExposureOffsetStepSize, []),
            returnValue: _i23.Future<double>.value(0.0),
          )
          as _i23.Future<double>);

  @override
  _i23.Future<double> setExposureOffset(double? offset) =>
      (super.noSuchMethod(
            Invocation.method(#setExposureOffset, [offset]),
            returnValue: _i23.Future<double>.value(0.0),
          )
          as _i23.Future<double>);

  @override
  _i23.Future<void> lockCaptureOrientation([
    _i2.DeviceOrientation? orientation,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#lockCaptureOrientation, [orientation]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> setFocusMode(_i12.FocusMode? mode) =>
      (super.noSuchMethod(
            Invocation.method(#setFocusMode, [mode]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> unlockCaptureOrientation() =>
      (super.noSuchMethod(
            Invocation.method(#unlockCaptureOrientation, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  _i23.Future<void> setFocusPoint(_i24.Offset? point) =>
      (super.noSuchMethod(
            Invocation.method(#setFocusPoint, [point]),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  bool supportsImageStreaming() =>
      (super.noSuchMethod(
            Invocation.method(#supportsImageStreaming, []),
            returnValue: false,
          )
          as bool);

  @override
  _i23.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i23.Future<void>.value(),
            returnValueForMissingStub: _i23.Future<void>.value(),
          )
          as _i23.Future<void>);

  @override
  void removeListener(_i24.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i24.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [PersonalUserKycRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockPersonalUserKycRepository extends _i1.Mock
    implements _i25.PersonalUserKycRepository {
  MockPersonalUserKycRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeApiClient_0(this, Invocation.getter(#apiClient)),
          )
          as _i2.ApiClient);

  @override
  _i23.Future<_i4.CommonSuccessModel> uploadPersonalKyc({
    required String? userID,
    required String? documentType,
    required String? documentNumber,
    required String? nameOnPan,
    required _i2.FileData? documentFrontImage,
    _i2.FileData? documentBackImage,
    required bool? isAddharCard,
    required String? userType,
    String? kycRole,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadPersonalKyc, [], {
              #userID: userID,
              #documentType: documentType,
              #documentNumber: documentNumber,
              #nameOnPan: nameOnPan,
              #documentFrontImage: documentFrontImage,
              #documentBackImage: documentBackImage,
              #isAddharCard: isAddharCard,
              #userType: userType,
              #kycRole: kycRole,
            }),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadPersonalKyc, [], {
                  #userID: userID,
                  #documentType: documentType,
                  #documentNumber: documentNumber,
                  #nameOnPan: nameOnPan,
                  #documentFrontImage: documentFrontImage,
                  #documentBackImage: documentBackImage,
                  #isAddharCard: isAddharCard,
                  #userType: userType,
                  #kycRole: kycRole,
                }),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i14.CaptchaModel> generateCaptcha() =>
      (super.noSuchMethod(
            Invocation.method(#generateCaptcha, []),
            returnValue: _i23.Future<_i14.CaptchaModel>.value(
              _FakeCaptchaModel_16(
                this,
                Invocation.method(#generateCaptcha, []),
              ),
            ),
          )
          as _i23.Future<_i14.CaptchaModel>);

  @override
  _i23.Future<_i15.RecaptchaModel> reGenerateCaptcha({
    required String? sessionId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#reGenerateCaptcha, [], {#sessionId: sessionId}),
            returnValue: _i23.Future<_i15.RecaptchaModel>.value(
              _FakeRecaptchaModel_17(
                this,
                Invocation.method(#reGenerateCaptcha, [], {
                  #sessionId: sessionId,
                }),
              ),
            ),
          )
          as _i23.Future<_i15.RecaptchaModel>);

  @override
  _i23.Future<_i16.AadharOTPSendModel> generateAadharOTP({
    required String? aadhaarNumber,
    required String? captcha,
    required String? sessionId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#generateAadharOTP, [], {
              #aadhaarNumber: aadhaarNumber,
              #captcha: captcha,
              #sessionId: sessionId,
            }),
            returnValue: _i23.Future<_i16.AadharOTPSendModel>.value(
              _FakeAadharOTPSendModel_18(
                this,
                Invocation.method(#generateAadharOTP, [], {
                  #aadhaarNumber: aadhaarNumber,
                  #captcha: captcha,
                  #sessionId: sessionId,
                }),
              ),
            ),
          )
          as _i23.Future<_i16.AadharOTPSendModel>);

  @override
  _i23.Future<_i17.AadharOTPVerifyModel> validateAadharOtp({
    required bool? faker,
    required String? otp,
    required String? sessionId,
    required String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateAadharOtp, [], {
              #faker: faker,
              #otp: otp,
              #sessionId: sessionId,
              #userId: userId,
            }),
            returnValue: _i23.Future<_i17.AadharOTPVerifyModel>.value(
              _FakeAadharOTPVerifyModel_19(
                this,
                Invocation.method(#validateAadharOtp, [], {
                  #faker: faker,
                  #otp: otp,
                  #sessionId: sessionId,
                  #userId: userId,
                }),
              ),
            ),
          )
          as _i23.Future<_i17.AadharOTPVerifyModel>);

  @override
  _i23.Future<_i18.GetPanDetailModel> getPanDetails({
    required String? panNumber,
    required String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getPanDetails, [], {
              #panNumber: panNumber,
              #userId: userId,
            }),
            returnValue: _i23.Future<_i18.GetPanDetailModel>.value(
              _FakeGetPanDetailModel_20(
                this,
                Invocation.method(#getPanDetails, [], {
                  #panNumber: panNumber,
                  #userId: userId,
                }),
              ),
            ),
          )
          as _i23.Future<_i18.GetPanDetailModel>);

  @override
  _i23.Future<_i19.GetCityAndStateModel> getCityAndState({
    required String? pincode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getCityAndState, [], {#pincode: pincode}),
            returnValue: _i23.Future<_i19.GetCityAndStateModel>.value(
              _FakeGetCityAndStateModel_21(
                this,
                Invocation.method(#getCityAndState, [], {#pincode: pincode}),
              ),
            ),
          )
          as _i23.Future<_i19.GetCityAndStateModel>);

  @override
  _i23.Future<_i4.CommonSuccessModel> uploadResidentialAddressDetails({
    required String? userID,
    required String? userType,
    required String? country,
    required String? pinCode,
    required String? state,
    required String? city,
    required String? addressLine1,
    String? addressLine2,
    required String? documentType,
    _i2.FileData? documentFrontImage,
    _i2.FileData? documentBackImage,
    required bool? isAddharCard,
    required String? aadhaarUsedAsIdentity,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadResidentialAddressDetails, [], {
              #userID: userID,
              #userType: userType,
              #country: country,
              #pinCode: pinCode,
              #state: state,
              #city: city,
              #addressLine1: addressLine1,
              #addressLine2: addressLine2,
              #documentType: documentType,
              #documentFrontImage: documentFrontImage,
              #documentBackImage: documentBackImage,
              #isAddharCard: isAddharCard,
              #aadhaarUsedAsIdentity: aadhaarUsedAsIdentity,
            }),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadResidentialAddressDetails, [], {
                  #userID: userID,
                  #userType: userType,
                  #country: country,
                  #pinCode: pinCode,
                  #state: state,
                  #city: city,
                  #addressLine1: addressLine1,
                  #addressLine2: addressLine2,
                  #documentType: documentType,
                  #documentFrontImage: documentFrontImage,
                  #documentBackImage: documentBackImage,
                  #isAddharCard: isAddharCard,
                  #aadhaarUsedAsIdentity: aadhaarUsedAsIdentity,
                }),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i20.GetGstDetailsModel> getGSTDetails({
    required String? userID,
    required String? estimatedAnnualIncome,
    required String? gstNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getGSTDetails, [], {
              #userID: userID,
              #estimatedAnnualIncome: estimatedAnnualIncome,
              #gstNumber: gstNumber,
            }),
            returnValue: _i23.Future<_i20.GetGstDetailsModel>.value(
              _FakeGetGstDetailsModel_22(
                this,
                Invocation.method(#getGSTDetails, [], {
                  #userID: userID,
                  #estimatedAnnualIncome: estimatedAnnualIncome,
                  #gstNumber: gstNumber,
                }),
              ),
            ),
          )
          as _i23.Future<_i20.GetGstDetailsModel>);

  @override
  _i23.Future<_i4.CommonSuccessModel> uploadGSTDocument({
    required String? userID,
    required String? gstNumber,
    required String? userType,
    _i2.FileData? gstCertificate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadGSTDocument, [], {
              #userID: userID,
              #gstNumber: gstNumber,
              #userType: userType,
              #gstCertificate: gstCertificate,
            }),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadGSTDocument, [], {
                  #userID: userID,
                  #gstNumber: gstNumber,
                  #userType: userType,
                  #gstCertificate: gstCertificate,
                }),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i4.CommonSuccessModel> uploadBusinessLegalDocuments({
    required String? userID,
    required String? userType,
    required String? documentType,
    String? documentNumber,
    _i2.FileData? documentFrontImage,
    _i2.FileData? documentbackImage,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadBusinessLegalDocuments, [], {
              #userID: userID,
              #userType: userType,
              #documentType: documentType,
              #documentNumber: documentNumber,
              #documentFrontImage: documentFrontImage,
              #documentbackImage: documentbackImage,
            }),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadBusinessLegalDocuments, [], {
                  #userID: userID,
                  #userType: userType,
                  #documentType: documentType,
                  #documentNumber: documentNumber,
                  #documentFrontImage: documentFrontImage,
                  #documentbackImage: documentbackImage,
                }),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i21.BankAccountVerifyModel> verifyBankAccount({
    required String? accountNumber,
    required String? ifscCode,
    required String? userID,
    required String? userType,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyBankAccount, [], {
              #accountNumber: accountNumber,
              #ifscCode: ifscCode,
              #userID: userID,
              #userType: userType,
            }),
            returnValue: _i23.Future<_i21.BankAccountVerifyModel>.value(
              _FakeBankAccountVerifyModel_23(
                this,
                Invocation.method(#verifyBankAccount, [], {
                  #accountNumber: accountNumber,
                  #ifscCode: ifscCode,
                  #userID: userID,
                  #userType: userType,
                }),
              ),
            ),
          )
          as _i23.Future<_i21.BankAccountVerifyModel>);

  @override
  _i23.Future<_i4.CommonSuccessModel> uploadBankDocuments({
    required String? userID,
    required String? userType,
    required String? accountNumber,
    required String? ifscCode,
    required String? documentType,
    required _i2.FileData? proofDocumentImage,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadBankDocuments, [], {
              #userID: userID,
              #userType: userType,
              #accountNumber: accountNumber,
              #ifscCode: ifscCode,
              #documentType: documentType,
              #proofDocumentImage: proofDocumentImage,
            }),
            returnValue: _i23.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadBankDocuments, [], {
                  #userID: userID,
                  #userType: userType,
                  #accountNumber: accountNumber,
                  #ifscCode: ifscCode,
                  #documentType: documentType,
                  #proofDocumentImage: proofDocumentImage,
                }),
              ),
            ),
          )
          as _i23.Future<_i4.CommonSuccessModel>);

  @override
  _i23.Future<_i22.PresignedUrlModel> getPresignedUrl({
    required String? urlPath,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getPresignedUrl, [], {#urlPath: urlPath}),
            returnValue: _i23.Future<_i22.PresignedUrlModel>.value(
              _FakePresignedUrlModel_24(
                this,
                Invocation.method(#getPresignedUrl, [], {#urlPath: urlPath}),
              ),
            ),
          )
          as _i23.Future<_i22.PresignedUrlModel>);
}
