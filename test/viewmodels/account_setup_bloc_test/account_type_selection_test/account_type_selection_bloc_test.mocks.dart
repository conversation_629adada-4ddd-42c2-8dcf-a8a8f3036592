// Mocks generated by Mocki<PERSON> 5.4.6 from annotations
// in exchek/test/viewmodels/account_setup_bloc_test/account_type_selection_test/account_type_selection_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i12;

import 'package:exchek/core/utils/exports.dart' as _i2;
import 'package:exchek/models/auth_models/common_success_model.dart' as _i4;
import 'package:exchek/models/auth_models/email_availabilty_model.dart' as _i6;
import 'package:exchek/models/auth_models/get_user_detail_model.dart' as _i11;
import 'package:exchek/models/auth_models/login_email_register_model.dart'
    as _i9;
import 'package:exchek/models/auth_models/mobile_availabilty_model.dart' as _i7;
import 'package:exchek/models/auth_models/validate_login_otp_model.dart' as _i5;
import 'package:exchek/models/auth_models/verify_email_model.dart' as _i3;
import 'package:exchek/models/personal_user_models/get_currency_model.dart'
    as _i10;
import 'package:exchek/models/personal_user_models/get_option_model.dart'
    as _i8;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeApiClient_0 extends _i1.SmartFake implements _i2.ApiClient {
  _FakeApiClient_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeOAuth2Config_1 extends _i1.SmartFake implements _i2.OAuth2Config {
  _FakeOAuth2Config_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeVerifyEmailModel_2 extends _i1.SmartFake
    implements _i3.VerifyEmailModel {
  _FakeVerifyEmailModel_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCommonSuccessModel_3 extends _i1.SmartFake
    implements _i4.CommonSuccessModel {
  _FakeCommonSuccessModel_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeValidateLoginOtpModel_4 extends _i1.SmartFake
    implements _i5.ValidateLoginOtpModel {
  _FakeValidateLoginOtpModel_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEmailAvailabilityModel_5 extends _i1.SmartFake
    implements _i6.EmailAvailabilityModel {
  _FakeEmailAvailabilityModel_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMobileAvailabilityModel_6 extends _i1.SmartFake
    implements _i7.MobileAvailabilityModel {
  _FakeMobileAvailabilityModel_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetDropdownOptionModel_7 extends _i1.SmartFake
    implements _i8.GetDropdownOptionModel {
  _FakeGetDropdownOptionModel_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLoginEmailPasswordModel_8 extends _i1.SmartFake
    implements _i9.LoginEmailPasswordModel {
  _FakeLoginEmailPasswordModel_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetCurrencyOptionModel_9 extends _i1.SmartFake
    implements _i10.GetCurrencyOptionModel {
  _FakeGetCurrencyOptionModel_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetUserDetailModel_10 extends _i1.SmartFake
    implements _i11.GetUserDetailModel {
  _FakeGetUserDetailModel_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i2.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeApiClient_0(this, Invocation.getter(#apiClient)),
          )
          as _i2.ApiClient);

  @override
  _i2.OAuth2Config get oauth2Config =>
      (super.noSuchMethod(
            Invocation.getter(#oauth2Config),
            returnValue: _FakeOAuth2Config_1(
              this,
              Invocation.getter(#oauth2Config),
            ),
          )
          as _i2.OAuth2Config);

  @override
  _i12.Future<_i3.VerifyEmailModel> sendEmailVerificationLink({
    required String? email,
    required String? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerificationLink, [], {
              #email: email,
              #type: type,
            }),
            returnValue: _i12.Future<_i3.VerifyEmailModel>.value(
              _FakeVerifyEmailModel_2(
                this,
                Invocation.method(#sendEmailVerificationLink, [], {
                  #email: email,
                  #type: type,
                }),
              ),
            ),
          )
          as _i12.Future<_i3.VerifyEmailModel>);

  @override
  _i12.Future<_i4.CommonSuccessModel> resetPasswordVerificationLink({
    required String? email,
    required String? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#resetPasswordVerificationLink, [], {
              #email: email,
              #type: type,
            }),
            returnValue: _i12.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#resetPasswordVerificationLink, [], {
                  #email: email,
                  #type: type,
                }),
              ),
            ),
          )
          as _i12.Future<_i4.CommonSuccessModel>);

  @override
  _i12.Future<_i4.CommonSuccessModel> sendOtp({
    required String? mobile,
    required String? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendOtp, [], {#mobile: mobile, #type: type}),
            returnValue: _i12.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#sendOtp, [], {#mobile: mobile, #type: type}),
              ),
            ),
          )
          as _i12.Future<_i4.CommonSuccessModel>);

  @override
  _i12.Future<_i5.ValidateLoginOtpModel> validateLoginOtp({
    required String? mobile,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateLoginOtp, [], {
              #mobile: mobile,
              #otp: otp,
            }),
            returnValue: _i12.Future<_i5.ValidateLoginOtpModel>.value(
              _FakeValidateLoginOtpModel_4(
                this,
                Invocation.method(#validateLoginOtp, [], {
                  #mobile: mobile,
                  #otp: otp,
                }),
              ),
            ),
          )
          as _i12.Future<_i5.ValidateLoginOtpModel>);

  @override
  _i12.Future<_i4.CommonSuccessModel> validateregistrationOtp({
    required String? mobile,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateregistrationOtp, [], {
              #mobile: mobile,
              #otp: otp,
            }),
            returnValue: _i12.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#validateregistrationOtp, [], {
                  #mobile: mobile,
                  #otp: otp,
                }),
              ),
            ),
          )
          as _i12.Future<_i4.CommonSuccessModel>);

  @override
  _i12.Future<_i4.CommonSuccessModel> validateForgotPasswordOtp({
    required String? mobile,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateForgotPasswordOtp, [], {
              #mobile: mobile,
              #otp: otp,
            }),
            returnValue: _i12.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#validateForgotPasswordOtp, [], {
                  #mobile: mobile,
                  #otp: otp,
                }),
              ),
            ),
          )
          as _i12.Future<_i4.CommonSuccessModel>);

  @override
  _i12.Future<_i4.CommonSuccessModel> updatePassword({
    required String? confirmpassword,
    required String? email,
    required String? mobilenumber,
    required String? newpassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #confirmpassword: confirmpassword,
              #email: email,
              #mobilenumber: mobilenumber,
              #newpassword: newpassword,
            }),
            returnValue: _i12.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#updatePassword, [], {
                  #confirmpassword: confirmpassword,
                  #email: email,
                  #mobilenumber: mobilenumber,
                  #newpassword: newpassword,
                }),
              ),
            ),
          )
          as _i12.Future<_i4.CommonSuccessModel>);

  @override
  _i12.Future<_i6.EmailAvailabilityModel> emailAvailability({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#emailAvailability, [], {#email: email}),
            returnValue: _i12.Future<_i6.EmailAvailabilityModel>.value(
              _FakeEmailAvailabilityModel_5(
                this,
                Invocation.method(#emailAvailability, [], {#email: email}),
              ),
            ),
          )
          as _i12.Future<_i6.EmailAvailabilityModel>);

  @override
  _i12.Future<_i7.MobileAvailabilityModel> mobileAvailability({
    required String? mobileNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#mobileAvailability, [], {
              #mobileNumber: mobileNumber,
            }),
            returnValue: _i12.Future<_i7.MobileAvailabilityModel>.value(
              _FakeMobileAvailabilityModel_6(
                this,
                Invocation.method(#mobileAvailability, [], {
                  #mobileNumber: mobileNumber,
                }),
              ),
            ),
          )
          as _i12.Future<_i7.MobileAvailabilityModel>);

  @override
  _i12.Future<_i8.GetDropdownOptionModel> getDropdownOptions() =>
      (super.noSuchMethod(
            Invocation.method(#getDropdownOptions, []),
            returnValue: _i12.Future<_i8.GetDropdownOptionModel>.value(
              _FakeGetDropdownOptionModel_7(
                this,
                Invocation.method(#getDropdownOptions, []),
              ),
            ),
          )
          as _i12.Future<_i8.GetDropdownOptionModel>);

  @override
  _i12.Future<_i9.LoginEmailPasswordModel> loginuser({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loginuser, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i12.Future<_i9.LoginEmailPasswordModel>.value(
              _FakeLoginEmailPasswordModel_8(
                this,
                Invocation.method(#loginuser, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i12.Future<_i9.LoginEmailPasswordModel>);

  @override
  _i12.Future<_i9.LoginEmailPasswordModel> registerPersonalUser({
    required String? email,
    required String? estimatedMonthlyVolume,
    required String? mobileNumber,
    required List<String>? multicurrency,
    required String? receivingreason,
    required List<String>? profession,
    required String? productDescription,
    required String? legalFullName,
    required String? password,
    required Map<dynamic, dynamic>? tosacceptance,
    required String? usertype,
    required String? website,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerPersonalUser, [], {
              #email: email,
              #estimatedMonthlyVolume: estimatedMonthlyVolume,
              #mobileNumber: mobileNumber,
              #multicurrency: multicurrency,
              #receivingreason: receivingreason,
              #profession: profession,
              #productDescription: productDescription,
              #legalFullName: legalFullName,
              #password: password,
              #tosacceptance: tosacceptance,
              #usertype: usertype,
              #website: website,
            }),
            returnValue: _i12.Future<_i9.LoginEmailPasswordModel>.value(
              _FakeLoginEmailPasswordModel_8(
                this,
                Invocation.method(#registerPersonalUser, [], {
                  #email: email,
                  #estimatedMonthlyVolume: estimatedMonthlyVolume,
                  #mobileNumber: mobileNumber,
                  #multicurrency: multicurrency,
                  #receivingreason: receivingreason,
                  #profession: profession,
                  #productDescription: productDescription,
                  #legalFullName: legalFullName,
                  #password: password,
                  #tosacceptance: tosacceptance,
                  #usertype: usertype,
                  #website: website,
                }),
              ),
            ),
          )
          as _i12.Future<_i9.LoginEmailPasswordModel>);

  @override
  _i12.Future<_i9.LoginEmailPasswordModel> registerBusinessUser({
    required String? email,
    required String? estimatedMonthlyVolume,
    required String? mobileNumber,
    required List<String>? multicurrency,
    required String? businesstype,
    required String? businessnature,
    required List<String>? exportstype,
    required String? businesslegalname,
    required String? password,
    required Map<dynamic, dynamic>? tosacceptance,
    required String? usertype,
    required String? username,
    required String? website,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerBusinessUser, [], {
              #email: email,
              #estimatedMonthlyVolume: estimatedMonthlyVolume,
              #mobileNumber: mobileNumber,
              #multicurrency: multicurrency,
              #businesstype: businesstype,
              #businessnature: businessnature,
              #exportstype: exportstype,
              #businesslegalname: businesslegalname,
              #password: password,
              #tosacceptance: tosacceptance,
              #usertype: usertype,
              #username: username,
              #website: website,
            }),
            returnValue: _i12.Future<_i9.LoginEmailPasswordModel>.value(
              _FakeLoginEmailPasswordModel_8(
                this,
                Invocation.method(#registerBusinessUser, [], {
                  #email: email,
                  #estimatedMonthlyVolume: estimatedMonthlyVolume,
                  #mobileNumber: mobileNumber,
                  #multicurrency: multicurrency,
                  #businesstype: businesstype,
                  #businessnature: businessnature,
                  #exportstype: exportstype,
                  #businesslegalname: businesslegalname,
                  #password: password,
                  #tosacceptance: tosacceptance,
                  #usertype: usertype,
                  #username: username,
                  #website: website,
                }),
              ),
            ),
          )
          as _i12.Future<_i9.LoginEmailPasswordModel>);

  @override
  _i12.Future<_i10.GetCurrencyOptionModel> getCurrencyOptions() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrencyOptions, []),
            returnValue: _i12.Future<_i10.GetCurrencyOptionModel>.value(
              _FakeGetCurrencyOptionModel_9(
                this,
                Invocation.method(#getCurrencyOptions, []),
              ),
            ),
          )
          as _i12.Future<_i10.GetCurrencyOptionModel>);

  @override
  _i12.Future<_i4.CommonSuccessModel> logout({required String? email}) =>
      (super.noSuchMethod(
            Invocation.method(#logout, [], {#email: email}),
            returnValue: _i12.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#logout, [], {#email: email}),
              ),
            ),
          )
          as _i12.Future<_i4.CommonSuccessModel>);

  @override
  _i12.Future<_i11.GetUserDetailModel> getUserDetails({
    required String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserDetails, [], {#userId: userId}),
            returnValue: _i12.Future<_i11.GetUserDetailModel>.value(
              _FakeGetUserDetailModel_10(
                this,
                Invocation.method(#getUserDetails, [], {#userId: userId}),
              ),
            ),
          )
          as _i12.Future<_i11.GetUserDetailModel>);
}
