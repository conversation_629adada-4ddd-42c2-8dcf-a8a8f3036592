// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in exchek/test/viewmodels/account_setup_bloc_test/business_account_setup_bloc_test/business_account_setup_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i21;

import 'package:exchek/core/utils/exports.dart' as _i2;
import 'package:exchek/models/auth_models/common_success_model.dart' as _i4;
import 'package:exchek/models/auth_models/email_availabilty_model.dart' as _i6;
import 'package:exchek/models/auth_models/get_user_detail_model.dart' as _i11;
import 'package:exchek/models/auth_models/login_email_register_model.dart'
    as _i9;
import 'package:exchek/models/auth_models/mobile_availabilty_model.dart' as _i7;
import 'package:exchek/models/auth_models/validate_login_otp_model.dart' as _i5;
import 'package:exchek/models/auth_models/verify_email_model.dart' as _i3;
import 'package:exchek/models/personal_user_models/aadhar_otp_model.dart'
    as _i14;
import 'package:exchek/models/personal_user_models/aadhar_verify_otp_model.dart'
    as _i15;
import 'package:exchek/models/personal_user_models/bank_account_verify_model.dart'
    as _i19;
import 'package:exchek/models/personal_user_models/captcha_model.dart' as _i12;
import 'package:exchek/models/personal_user_models/get_city_and_state_model.dart'
    as _i18;
import 'package:exchek/models/personal_user_models/get_currency_model.dart'
    as _i10;
import 'package:exchek/models/personal_user_models/get_gst_details_model.dart'
    as _i16;
import 'package:exchek/models/personal_user_models/get_option_model.dart'
    as _i8;
import 'package:exchek/models/personal_user_models/get_pan_detail_model.dart'
    as _i17;
import 'package:exchek/models/personal_user_models/presigned_url_model.dart'
    as _i20;
import 'package:exchek/models/personal_user_models/recaptcha_model.dart'
    as _i13;
import 'package:exchek/repository/business_user_kyc_repository.dart' as _i22;
import 'package:exchek/repository/personal_user_kyc_repository.dart' as _i23;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeApiClient_0 extends _i1.SmartFake implements _i2.ApiClient {
  _FakeApiClient_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeOAuth2Config_1 extends _i1.SmartFake implements _i2.OAuth2Config {
  _FakeOAuth2Config_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeVerifyEmailModel_2 extends _i1.SmartFake
    implements _i3.VerifyEmailModel {
  _FakeVerifyEmailModel_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCommonSuccessModel_3 extends _i1.SmartFake
    implements _i4.CommonSuccessModel {
  _FakeCommonSuccessModel_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeValidateLoginOtpModel_4 extends _i1.SmartFake
    implements _i5.ValidateLoginOtpModel {
  _FakeValidateLoginOtpModel_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEmailAvailabilityModel_5 extends _i1.SmartFake
    implements _i6.EmailAvailabilityModel {
  _FakeEmailAvailabilityModel_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMobileAvailabilityModel_6 extends _i1.SmartFake
    implements _i7.MobileAvailabilityModel {
  _FakeMobileAvailabilityModel_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetDropdownOptionModel_7 extends _i1.SmartFake
    implements _i8.GetDropdownOptionModel {
  _FakeGetDropdownOptionModel_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLoginEmailPasswordModel_8 extends _i1.SmartFake
    implements _i9.LoginEmailPasswordModel {
  _FakeLoginEmailPasswordModel_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetCurrencyOptionModel_9 extends _i1.SmartFake
    implements _i10.GetCurrencyOptionModel {
  _FakeGetCurrencyOptionModel_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetUserDetailModel_10 extends _i1.SmartFake
    implements _i11.GetUserDetailModel {
  _FakeGetUserDetailModel_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCaptchaModel_11 extends _i1.SmartFake implements _i12.CaptchaModel {
  _FakeCaptchaModel_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRecaptchaModel_12 extends _i1.SmartFake
    implements _i13.RecaptchaModel {
  _FakeRecaptchaModel_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAadharOTPSendModel_13 extends _i1.SmartFake
    implements _i14.AadharOTPSendModel {
  _FakeAadharOTPSendModel_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAadharOTPVerifyModel_14 extends _i1.SmartFake
    implements _i15.AadharOTPVerifyModel {
  _FakeAadharOTPVerifyModel_14(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetGstDetailsModel_15 extends _i1.SmartFake
    implements _i16.GetGstDetailsModel {
  _FakeGetGstDetailsModel_15(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetPanDetailModel_16 extends _i1.SmartFake
    implements _i17.GetPanDetailModel {
  _FakeGetPanDetailModel_16(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGetCityAndStateModel_17 extends _i1.SmartFake
    implements _i18.GetCityAndStateModel {
  _FakeGetCityAndStateModel_17(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBankAccountVerifyModel_18 extends _i1.SmartFake
    implements _i19.BankAccountVerifyModel {
  _FakeBankAccountVerifyModel_18(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePresignedUrlModel_19 extends _i1.SmartFake
    implements _i20.PresignedUrlModel {
  _FakePresignedUrlModel_19(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i2.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeApiClient_0(this, Invocation.getter(#apiClient)),
          )
          as _i2.ApiClient);

  @override
  _i2.OAuth2Config get oauth2Config =>
      (super.noSuchMethod(
            Invocation.getter(#oauth2Config),
            returnValue: _FakeOAuth2Config_1(
              this,
              Invocation.getter(#oauth2Config),
            ),
          )
          as _i2.OAuth2Config);

  @override
  _i21.Future<_i3.VerifyEmailModel> sendEmailVerificationLink({
    required String? email,
    required String? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerificationLink, [], {
              #email: email,
              #type: type,
            }),
            returnValue: _i21.Future<_i3.VerifyEmailModel>.value(
              _FakeVerifyEmailModel_2(
                this,
                Invocation.method(#sendEmailVerificationLink, [], {
                  #email: email,
                  #type: type,
                }),
              ),
            ),
          )
          as _i21.Future<_i3.VerifyEmailModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> resetPasswordVerificationLink({
    required String? email,
    required String? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#resetPasswordVerificationLink, [], {
              #email: email,
              #type: type,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#resetPasswordVerificationLink, [], {
                  #email: email,
                  #type: type,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> sendOtp({
    required String? mobile,
    required String? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendOtp, [], {#mobile: mobile, #type: type}),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#sendOtp, [], {#mobile: mobile, #type: type}),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i5.ValidateLoginOtpModel> validateLoginOtp({
    required String? mobile,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateLoginOtp, [], {
              #mobile: mobile,
              #otp: otp,
            }),
            returnValue: _i21.Future<_i5.ValidateLoginOtpModel>.value(
              _FakeValidateLoginOtpModel_4(
                this,
                Invocation.method(#validateLoginOtp, [], {
                  #mobile: mobile,
                  #otp: otp,
                }),
              ),
            ),
          )
          as _i21.Future<_i5.ValidateLoginOtpModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> validateregistrationOtp({
    required String? mobile,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateregistrationOtp, [], {
              #mobile: mobile,
              #otp: otp,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#validateregistrationOtp, [], {
                  #mobile: mobile,
                  #otp: otp,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> validateForgotPasswordOtp({
    required String? mobile,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateForgotPasswordOtp, [], {
              #mobile: mobile,
              #otp: otp,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#validateForgotPasswordOtp, [], {
                  #mobile: mobile,
                  #otp: otp,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> updatePassword({
    required String? confirmpassword,
    required String? email,
    required String? mobilenumber,
    required String? newpassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #confirmpassword: confirmpassword,
              #email: email,
              #mobilenumber: mobilenumber,
              #newpassword: newpassword,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#updatePassword, [], {
                  #confirmpassword: confirmpassword,
                  #email: email,
                  #mobilenumber: mobilenumber,
                  #newpassword: newpassword,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i6.EmailAvailabilityModel> emailAvailability({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#emailAvailability, [], {#email: email}),
            returnValue: _i21.Future<_i6.EmailAvailabilityModel>.value(
              _FakeEmailAvailabilityModel_5(
                this,
                Invocation.method(#emailAvailability, [], {#email: email}),
              ),
            ),
          )
          as _i21.Future<_i6.EmailAvailabilityModel>);

  @override
  _i21.Future<_i7.MobileAvailabilityModel> mobileAvailability({
    required String? mobileNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#mobileAvailability, [], {
              #mobileNumber: mobileNumber,
            }),
            returnValue: _i21.Future<_i7.MobileAvailabilityModel>.value(
              _FakeMobileAvailabilityModel_6(
                this,
                Invocation.method(#mobileAvailability, [], {
                  #mobileNumber: mobileNumber,
                }),
              ),
            ),
          )
          as _i21.Future<_i7.MobileAvailabilityModel>);

  @override
  _i21.Future<_i8.GetDropdownOptionModel> getDropdownOptions() =>
      (super.noSuchMethod(
            Invocation.method(#getDropdownOptions, []),
            returnValue: _i21.Future<_i8.GetDropdownOptionModel>.value(
              _FakeGetDropdownOptionModel_7(
                this,
                Invocation.method(#getDropdownOptions, []),
              ),
            ),
          )
          as _i21.Future<_i8.GetDropdownOptionModel>);

  @override
  _i21.Future<_i9.LoginEmailPasswordModel> loginuser({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loginuser, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i21.Future<_i9.LoginEmailPasswordModel>.value(
              _FakeLoginEmailPasswordModel_8(
                this,
                Invocation.method(#loginuser, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i21.Future<_i9.LoginEmailPasswordModel>);

  @override
  _i21.Future<_i9.LoginEmailPasswordModel> registerPersonalUser({
    required String? email,
    required String? estimatedMonthlyVolume,
    required String? mobileNumber,
    required List<String>? multicurrency,
    required String? receivingreason,
    required List<String>? profession,
    required String? productDescription,
    required String? legalFullName,
    required String? password,
    required Map<dynamic, dynamic>? tosacceptance,
    required String? usertype,
    required String? website,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerPersonalUser, [], {
              #email: email,
              #estimatedMonthlyVolume: estimatedMonthlyVolume,
              #mobileNumber: mobileNumber,
              #multicurrency: multicurrency,
              #receivingreason: receivingreason,
              #profession: profession,
              #productDescription: productDescription,
              #legalFullName: legalFullName,
              #password: password,
              #tosacceptance: tosacceptance,
              #usertype: usertype,
              #website: website,
            }),
            returnValue: _i21.Future<_i9.LoginEmailPasswordModel>.value(
              _FakeLoginEmailPasswordModel_8(
                this,
                Invocation.method(#registerPersonalUser, [], {
                  #email: email,
                  #estimatedMonthlyVolume: estimatedMonthlyVolume,
                  #mobileNumber: mobileNumber,
                  #multicurrency: multicurrency,
                  #receivingreason: receivingreason,
                  #profession: profession,
                  #productDescription: productDescription,
                  #legalFullName: legalFullName,
                  #password: password,
                  #tosacceptance: tosacceptance,
                  #usertype: usertype,
                  #website: website,
                }),
              ),
            ),
          )
          as _i21.Future<_i9.LoginEmailPasswordModel>);

  @override
  _i21.Future<_i9.LoginEmailPasswordModel> registerBusinessUser({
    required String? email,
    required String? estimatedMonthlyVolume,
    required String? mobileNumber,
    required List<String>? multicurrency,
    required String? businesstype,
    required String? businessnature,
    required List<String>? exportstype,
    required String? businesslegalname,
    required String? password,
    required Map<dynamic, dynamic>? tosacceptance,
    required String? usertype,
    required String? username,
    required String? website,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerBusinessUser, [], {
              #email: email,
              #estimatedMonthlyVolume: estimatedMonthlyVolume,
              #mobileNumber: mobileNumber,
              #multicurrency: multicurrency,
              #businesstype: businesstype,
              #businessnature: businessnature,
              #exportstype: exportstype,
              #businesslegalname: businesslegalname,
              #password: password,
              #tosacceptance: tosacceptance,
              #usertype: usertype,
              #username: username,
              #website: website,
            }),
            returnValue: _i21.Future<_i9.LoginEmailPasswordModel>.value(
              _FakeLoginEmailPasswordModel_8(
                this,
                Invocation.method(#registerBusinessUser, [], {
                  #email: email,
                  #estimatedMonthlyVolume: estimatedMonthlyVolume,
                  #mobileNumber: mobileNumber,
                  #multicurrency: multicurrency,
                  #businesstype: businesstype,
                  #businessnature: businessnature,
                  #exportstype: exportstype,
                  #businesslegalname: businesslegalname,
                  #password: password,
                  #tosacceptance: tosacceptance,
                  #usertype: usertype,
                  #username: username,
                  #website: website,
                }),
              ),
            ),
          )
          as _i21.Future<_i9.LoginEmailPasswordModel>);

  @override
  _i21.Future<_i10.GetCurrencyOptionModel> getCurrencyOptions() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrencyOptions, []),
            returnValue: _i21.Future<_i10.GetCurrencyOptionModel>.value(
              _FakeGetCurrencyOptionModel_9(
                this,
                Invocation.method(#getCurrencyOptions, []),
              ),
            ),
          )
          as _i21.Future<_i10.GetCurrencyOptionModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> logout({required String? email}) =>
      (super.noSuchMethod(
            Invocation.method(#logout, [], {#email: email}),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#logout, [], {#email: email}),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i11.GetUserDetailModel> getUserDetails({
    required String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserDetails, [], {#userId: userId}),
            returnValue: _i21.Future<_i11.GetUserDetailModel>.value(
              _FakeGetUserDetailModel_10(
                this,
                Invocation.method(#getUserDetails, [], {#userId: userId}),
              ),
            ),
          )
          as _i21.Future<_i11.GetUserDetailModel>);
}

/// A class which mocks [BusinessUserKycRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockBusinessUserKycRepository extends _i1.Mock
    implements _i22.BusinessUserKycRepository {
  MockBusinessUserKycRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeApiClient_0(this, Invocation.getter(#apiClient)),
          )
          as _i2.ApiClient);

  @override
  _i21.Future<_i4.CommonSuccessModel> uploadbusinessKyc({
    required String? userID,
    required String? documentType,
    required String? documentNumber,
    required String? nameOnPan,
    required _i2.FileData? documentFrontImage,
    _i2.FileData? documentBackImage,
    required bool? isAddharCard,
    required String? userType,
    String? kycRole,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadbusinessKyc, [], {
              #userID: userID,
              #documentType: documentType,
              #documentNumber: documentNumber,
              #nameOnPan: nameOnPan,
              #documentFrontImage: documentFrontImage,
              #documentBackImage: documentBackImage,
              #isAddharCard: isAddharCard,
              #userType: userType,
              #kycRole: kycRole,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadbusinessKyc, [], {
                  #userID: userID,
                  #documentType: documentType,
                  #documentNumber: documentNumber,
                  #nameOnPan: nameOnPan,
                  #documentFrontImage: documentFrontImage,
                  #documentBackImage: documentBackImage,
                  #isAddharCard: isAddharCard,
                  #userType: userType,
                  #kycRole: kycRole,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i12.CaptchaModel> generateCaptcha() =>
      (super.noSuchMethod(
            Invocation.method(#generateCaptcha, []),
            returnValue: _i21.Future<_i12.CaptchaModel>.value(
              _FakeCaptchaModel_11(
                this,
                Invocation.method(#generateCaptcha, []),
              ),
            ),
          )
          as _i21.Future<_i12.CaptchaModel>);

  @override
  _i21.Future<_i13.RecaptchaModel> reGenerateCaptcha({
    required String? sessionId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#reGenerateCaptcha, [], {#sessionId: sessionId}),
            returnValue: _i21.Future<_i13.RecaptchaModel>.value(
              _FakeRecaptchaModel_12(
                this,
                Invocation.method(#reGenerateCaptcha, [], {
                  #sessionId: sessionId,
                }),
              ),
            ),
          )
          as _i21.Future<_i13.RecaptchaModel>);

  @override
  _i21.Future<_i14.AadharOTPSendModel> generateAadharOTP({
    required String? aadhaarNumber,
    required String? captcha,
    required String? sessionId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#generateAadharOTP, [], {
              #aadhaarNumber: aadhaarNumber,
              #captcha: captcha,
              #sessionId: sessionId,
            }),
            returnValue: _i21.Future<_i14.AadharOTPSendModel>.value(
              _FakeAadharOTPSendModel_13(
                this,
                Invocation.method(#generateAadharOTP, [], {
                  #aadhaarNumber: aadhaarNumber,
                  #captcha: captcha,
                  #sessionId: sessionId,
                }),
              ),
            ),
          )
          as _i21.Future<_i14.AadharOTPSendModel>);

  @override
  _i21.Future<_i15.AadharOTPVerifyModel> validateAadharOtp({
    required bool? faker,
    required String? otp,
    required String? sessionId,
    required String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateAadharOtp, [], {
              #faker: faker,
              #otp: otp,
              #sessionId: sessionId,
              #userId: userId,
            }),
            returnValue: _i21.Future<_i15.AadharOTPVerifyModel>.value(
              _FakeAadharOTPVerifyModel_14(
                this,
                Invocation.method(#validateAadharOtp, [], {
                  #faker: faker,
                  #otp: otp,
                  #sessionId: sessionId,
                  #userId: userId,
                }),
              ),
            ),
          )
          as _i21.Future<_i15.AadharOTPVerifyModel>);

  @override
  _i21.Future<_i16.GetGstDetailsModel> getGSTDetails({
    required String? userID,
    required String? estimatedAnnualIncome,
    required String? gstNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getGSTDetails, [], {
              #userID: userID,
              #estimatedAnnualIncome: estimatedAnnualIncome,
              #gstNumber: gstNumber,
            }),
            returnValue: _i21.Future<_i16.GetGstDetailsModel>.value(
              _FakeGetGstDetailsModel_15(
                this,
                Invocation.method(#getGSTDetails, [], {
                  #userID: userID,
                  #estimatedAnnualIncome: estimatedAnnualIncome,
                  #gstNumber: gstNumber,
                }),
              ),
            ),
          )
          as _i21.Future<_i16.GetGstDetailsModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> uploadGSTDocument({
    required String? userID,
    required String? gstNumber,
    required String? userType,
    _i2.FileData? gstCertificate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadGSTDocument, [], {
              #userID: userID,
              #gstNumber: gstNumber,
              #userType: userType,
              #gstCertificate: gstCertificate,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadGSTDocument, [], {
                  #userID: userID,
                  #gstNumber: gstNumber,
                  #userType: userType,
                  #gstCertificate: gstCertificate,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);
}

/// A class which mocks [PersonalUserKycRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockPersonalUserKycRepository extends _i1.Mock
    implements _i23.PersonalUserKycRepository {
  MockPersonalUserKycRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeApiClient_0(this, Invocation.getter(#apiClient)),
          )
          as _i2.ApiClient);

  @override
  _i21.Future<_i4.CommonSuccessModel> uploadPersonalKyc({
    required String? userID,
    required String? documentType,
    required String? documentNumber,
    required String? nameOnPan,
    required _i2.FileData? documentFrontImage,
    _i2.FileData? documentBackImage,
    required bool? isAddharCard,
    required String? userType,
    String? kycRole,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadPersonalKyc, [], {
              #userID: userID,
              #documentType: documentType,
              #documentNumber: documentNumber,
              #nameOnPan: nameOnPan,
              #documentFrontImage: documentFrontImage,
              #documentBackImage: documentBackImage,
              #isAddharCard: isAddharCard,
              #userType: userType,
              #kycRole: kycRole,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadPersonalKyc, [], {
                  #userID: userID,
                  #documentType: documentType,
                  #documentNumber: documentNumber,
                  #nameOnPan: nameOnPan,
                  #documentFrontImage: documentFrontImage,
                  #documentBackImage: documentBackImage,
                  #isAddharCard: isAddharCard,
                  #userType: userType,
                  #kycRole: kycRole,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i12.CaptchaModel> generateCaptcha() =>
      (super.noSuchMethod(
            Invocation.method(#generateCaptcha, []),
            returnValue: _i21.Future<_i12.CaptchaModel>.value(
              _FakeCaptchaModel_11(
                this,
                Invocation.method(#generateCaptcha, []),
              ),
            ),
          )
          as _i21.Future<_i12.CaptchaModel>);

  @override
  _i21.Future<_i13.RecaptchaModel> reGenerateCaptcha({
    required String? sessionId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#reGenerateCaptcha, [], {#sessionId: sessionId}),
            returnValue: _i21.Future<_i13.RecaptchaModel>.value(
              _FakeRecaptchaModel_12(
                this,
                Invocation.method(#reGenerateCaptcha, [], {
                  #sessionId: sessionId,
                }),
              ),
            ),
          )
          as _i21.Future<_i13.RecaptchaModel>);

  @override
  _i21.Future<_i14.AadharOTPSendModel> generateAadharOTP({
    required String? aadhaarNumber,
    required String? captcha,
    required String? sessionId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#generateAadharOTP, [], {
              #aadhaarNumber: aadhaarNumber,
              #captcha: captcha,
              #sessionId: sessionId,
            }),
            returnValue: _i21.Future<_i14.AadharOTPSendModel>.value(
              _FakeAadharOTPSendModel_13(
                this,
                Invocation.method(#generateAadharOTP, [], {
                  #aadhaarNumber: aadhaarNumber,
                  #captcha: captcha,
                  #sessionId: sessionId,
                }),
              ),
            ),
          )
          as _i21.Future<_i14.AadharOTPSendModel>);

  @override
  _i21.Future<_i15.AadharOTPVerifyModel> validateAadharOtp({
    required bool? faker,
    required String? otp,
    required String? sessionId,
    required String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateAadharOtp, [], {
              #faker: faker,
              #otp: otp,
              #sessionId: sessionId,
              #userId: userId,
            }),
            returnValue: _i21.Future<_i15.AadharOTPVerifyModel>.value(
              _FakeAadharOTPVerifyModel_14(
                this,
                Invocation.method(#validateAadharOtp, [], {
                  #faker: faker,
                  #otp: otp,
                  #sessionId: sessionId,
                  #userId: userId,
                }),
              ),
            ),
          )
          as _i21.Future<_i15.AadharOTPVerifyModel>);

  @override
  _i21.Future<_i17.GetPanDetailModel> getPanDetails({
    required String? panNumber,
    required String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getPanDetails, [], {
              #panNumber: panNumber,
              #userId: userId,
            }),
            returnValue: _i21.Future<_i17.GetPanDetailModel>.value(
              _FakeGetPanDetailModel_16(
                this,
                Invocation.method(#getPanDetails, [], {
                  #panNumber: panNumber,
                  #userId: userId,
                }),
              ),
            ),
          )
          as _i21.Future<_i17.GetPanDetailModel>);

  @override
  _i21.Future<_i18.GetCityAndStateModel> getCityAndState({
    required String? pincode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getCityAndState, [], {#pincode: pincode}),
            returnValue: _i21.Future<_i18.GetCityAndStateModel>.value(
              _FakeGetCityAndStateModel_17(
                this,
                Invocation.method(#getCityAndState, [], {#pincode: pincode}),
              ),
            ),
          )
          as _i21.Future<_i18.GetCityAndStateModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> uploadResidentialAddressDetails({
    required String? userID,
    required String? userType,
    required String? country,
    required String? pinCode,
    required String? state,
    required String? city,
    required String? addressLine1,
    String? addressLine2,
    required String? documentType,
    _i2.FileData? documentFrontImage,
    _i2.FileData? documentBackImage,
    required bool? isAddharCard,
    required String? aadhaarUsedAsIdentity,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadResidentialAddressDetails, [], {
              #userID: userID,
              #userType: userType,
              #country: country,
              #pinCode: pinCode,
              #state: state,
              #city: city,
              #addressLine1: addressLine1,
              #addressLine2: addressLine2,
              #documentType: documentType,
              #documentFrontImage: documentFrontImage,
              #documentBackImage: documentBackImage,
              #isAddharCard: isAddharCard,
              #aadhaarUsedAsIdentity: aadhaarUsedAsIdentity,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadResidentialAddressDetails, [], {
                  #userID: userID,
                  #userType: userType,
                  #country: country,
                  #pinCode: pinCode,
                  #state: state,
                  #city: city,
                  #addressLine1: addressLine1,
                  #addressLine2: addressLine2,
                  #documentType: documentType,
                  #documentFrontImage: documentFrontImage,
                  #documentBackImage: documentBackImage,
                  #isAddharCard: isAddharCard,
                  #aadhaarUsedAsIdentity: aadhaarUsedAsIdentity,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i16.GetGstDetailsModel> getGSTDetails({
    required String? userID,
    required String? estimatedAnnualIncome,
    required String? gstNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getGSTDetails, [], {
              #userID: userID,
              #estimatedAnnualIncome: estimatedAnnualIncome,
              #gstNumber: gstNumber,
            }),
            returnValue: _i21.Future<_i16.GetGstDetailsModel>.value(
              _FakeGetGstDetailsModel_15(
                this,
                Invocation.method(#getGSTDetails, [], {
                  #userID: userID,
                  #estimatedAnnualIncome: estimatedAnnualIncome,
                  #gstNumber: gstNumber,
                }),
              ),
            ),
          )
          as _i21.Future<_i16.GetGstDetailsModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> uploadGSTDocument({
    required String? userID,
    required String? gstNumber,
    required String? userType,
    _i2.FileData? gstCertificate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadGSTDocument, [], {
              #userID: userID,
              #gstNumber: gstNumber,
              #userType: userType,
              #gstCertificate: gstCertificate,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadGSTDocument, [], {
                  #userID: userID,
                  #gstNumber: gstNumber,
                  #userType: userType,
                  #gstCertificate: gstCertificate,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> uploadBusinessLegalDocuments({
    required String? userID,
    required String? userType,
    required String? documentType,
    String? documentNumber,
    _i2.FileData? documentFrontImage,
    _i2.FileData? documentbackImage,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadBusinessLegalDocuments, [], {
              #userID: userID,
              #userType: userType,
              #documentType: documentType,
              #documentNumber: documentNumber,
              #documentFrontImage: documentFrontImage,
              #documentbackImage: documentbackImage,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadBusinessLegalDocuments, [], {
                  #userID: userID,
                  #userType: userType,
                  #documentType: documentType,
                  #documentNumber: documentNumber,
                  #documentFrontImage: documentFrontImage,
                  #documentbackImage: documentbackImage,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i19.BankAccountVerifyModel> verifyBankAccount({
    required String? accountNumber,
    required String? ifscCode,
    required String? userID,
    required String? userType,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyBankAccount, [], {
              #accountNumber: accountNumber,
              #ifscCode: ifscCode,
              #userID: userID,
              #userType: userType,
            }),
            returnValue: _i21.Future<_i19.BankAccountVerifyModel>.value(
              _FakeBankAccountVerifyModel_18(
                this,
                Invocation.method(#verifyBankAccount, [], {
                  #accountNumber: accountNumber,
                  #ifscCode: ifscCode,
                  #userID: userID,
                  #userType: userType,
                }),
              ),
            ),
          )
          as _i21.Future<_i19.BankAccountVerifyModel>);

  @override
  _i21.Future<_i4.CommonSuccessModel> uploadBankDocuments({
    required String? userID,
    required String? userType,
    required String? accountNumber,
    required String? ifscCode,
    required String? documentType,
    required _i2.FileData? proofDocumentImage,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadBankDocuments, [], {
              #userID: userID,
              #userType: userType,
              #accountNumber: accountNumber,
              #ifscCode: ifscCode,
              #documentType: documentType,
              #proofDocumentImage: proofDocumentImage,
            }),
            returnValue: _i21.Future<_i4.CommonSuccessModel>.value(
              _FakeCommonSuccessModel_3(
                this,
                Invocation.method(#uploadBankDocuments, [], {
                  #userID: userID,
                  #userType: userType,
                  #accountNumber: accountNumber,
                  #ifscCode: ifscCode,
                  #documentType: documentType,
                  #proofDocumentImage: proofDocumentImage,
                }),
              ),
            ),
          )
          as _i21.Future<_i4.CommonSuccessModel>);

  @override
  _i21.Future<_i20.PresignedUrlModel> getPresignedUrl({
    required String? urlPath,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getPresignedUrl, [], {#urlPath: urlPath}),
            returnValue: _i21.Future<_i20.PresignedUrlModel>.value(
              _FakePresignedUrlModel_19(
                this,
                Invocation.method(#getPresignedUrl, [], {#urlPath: urlPath}),
              ),
            ),
          )
          as _i21.Future<_i20.PresignedUrlModel>);
}
