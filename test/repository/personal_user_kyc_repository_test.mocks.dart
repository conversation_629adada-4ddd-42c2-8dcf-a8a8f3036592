// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in exchek/test/repository/personal_user_kyc_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;
import 'dart:typed_data' as _i5;

import 'package:exchek/core/utils/exports.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ApiClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiClient extends _i1.Mock implements _i2.ApiClient {
  MockApiClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> buildHeaders() =>
      (super.noSuchMethod(
            Invocation.method(#buildHeaders, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<Map<String, dynamic>> request(
    _i2.RequestType? type,
    String? path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? multipartData,
    bool? isShowToast = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #request,
              [type, path],
              {
                #data: data,
                #multipartData: multipartData,
                #isShowToast: isShowToast,
              },
            ),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);
}

/// A class which mocks [FileData].
///
/// See the documentation for Mockito's code generation for more information.
class MockFileData extends _i1.Mock implements _i2.FileData {
  MockFileData() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get name =>
      (super.noSuchMethod(
            Invocation.getter(#name),
            returnValue: _i4.dummyValue<String>(this, Invocation.getter(#name)),
          )
          as String);

  @override
  _i5.Uint8List get bytes =>
      (super.noSuchMethod(
            Invocation.getter(#bytes),
            returnValue: _i5.Uint8List(0),
          )
          as _i5.Uint8List);

  @override
  double get sizeInMB =>
      (super.noSuchMethod(Invocation.getter(#sizeInMB), returnValue: 0.0)
          as double);
}
