// Mocks generated by Mockito 5.4.6 from annotations
// in exchek/test/repository/auth_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:exchek/core/utils/exports.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:oauth2_client/oauth2_helper.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeOAuth2Helper_0 extends _i1.SmartFake implements _i2.OAuth2Helper {
  _FakeOAuth2Helper_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ApiClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiClient extends _i1.Mock implements _i3.ApiClient {
  MockApiClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> buildHeaders() =>
      (super.noSuchMethod(
            Invocation.method(#buildHeaders, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<Map<String, dynamic>> request(
    _i3.RequestType? type,
    String? path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? multipartData,
    bool? isShowToast = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #request,
              [type, path],
              {
                #data: data,
                #multipartData: multipartData,
                #isShowToast: isShowToast,
              },
            ),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);
}

/// A class which mocks [OAuth2Config].
///
/// See the documentation for Mockito's code generation for more information.
class MockOAuth2Config extends _i1.Mock implements _i3.OAuth2Config {
  MockOAuth2Config() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.OAuth2Helper get googleHelper =>
      (super.noSuchMethod(
            Invocation.getter(#googleHelper),
            returnValue: _FakeOAuth2Helper_0(
              this,
              Invocation.getter(#googleHelper),
            ),
          )
          as _i2.OAuth2Helper);

  @override
  _i2.OAuth2Helper get linkedInHelper =>
      (super.noSuchMethod(
            Invocation.getter(#linkedInHelper),
            returnValue: _FakeOAuth2Helper_0(
              this,
              Invocation.getter(#linkedInHelper),
            ),
          )
          as _i2.OAuth2Helper);

  @override
  _i2.OAuth2Helper get appleHelper =>
      (super.noSuchMethod(
            Invocation.getter(#appleHelper),
            returnValue: _FakeOAuth2Helper_0(
              this,
              Invocation.getter(#appleHelper),
            ),
          )
          as _i2.OAuth2Helper);

  @override
  set googleHelper(_i2.OAuth2Helper? _googleHelper) => super.noSuchMethod(
    Invocation.setter(#googleHelper, _googleHelper),
    returnValueForMissingStub: null,
  );

  @override
  set linkedInHelper(_i2.OAuth2Helper? _linkedInHelper) => super.noSuchMethod(
    Invocation.setter(#linkedInHelper, _linkedInHelper),
    returnValueForMissingStub: null,
  );

  @override
  set appleHelper(_i2.OAuth2Helper? _appleHelper) => super.noSuchMethod(
    Invocation.setter(#appleHelper, _appleHelper),
    returnValueForMissingStub: null,
  );

  @override
  void initialize() => super.noSuchMethod(
    Invocation.method(#initialize, []),
    returnValueForMissingStub: null,
  );
}
