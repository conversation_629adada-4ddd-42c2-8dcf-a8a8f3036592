import 'package:exchek/repository/business_user_kyc_repository.dart';
import 'package:exchek/core/api_config/client/api_client.dart';
import 'package:exchek/models/auth_models/common_success_model.dart';
import 'package:exchek/models/personal_user_models/get_gst_details_model.dart';
import 'package:exchek/widgets/common_widget/app_file_upload_widget.dart';
import 'package:exchek/models/personal_user_models/aadhar_otp_model.dart';
import 'package:exchek/models/personal_user_models/aadhar_verify_otp_model.dart';
import 'package:exchek/models/personal_user_models/captcha_model.dart';
import 'package:exchek/models/personal_user_models/recaptcha_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:exchek/core/flavor_config/env_config.dart';
import 'package:exchek/core/enums/app_enums.dart';
import 'package:exchek/core/flavor_config/flavor_config.dart';

import 'business_user_kyc_repository_test.mocks.dart';

@GenerateMocks([ApiClient, FileData])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() {
    // Initialize FlavorConfig for testing
    const testBaseUrl = 'https://test-api.example.com';
    final testEnvConfig = EnvConfig(baseUrl: testBaseUrl);
    FlavorConfig.initialize(flavor: Flavor.dev, env: testEnvConfig);
  });

  late MockApiClient mockApiClient;
  late BusinessUserKycRepository repository;
  late FileData mockFileData;

  setUp(() {
    mockApiClient = MockApiClient();
    repository = BusinessUserKycRepository(apiClient: mockApiClient);
    mockFileData = MockFileData();
  });

  group('uploadbusinessKyc', () {
    test('success with Aadhaar card (back image included)', () async {
      when(
        mockApiClient.request(any, any, multipartData: anyNamed('multipartData')),
      ).thenAnswer((_) async => <String, dynamic>{'success': true});

      final result = await repository.uploadbusinessKyc(
        userID: '1',
        documentType: 'AADHAAR',
        documentNumber: '123412341234',
        nameOnPan: 'Test User',
        documentFrontImage: mockFileData,
        documentBackImage: mockFileData,
        isAddharCard: true,
        userType: 'business',
        kycRole: 'director',
      );

      expect(result, isA<CommonSuccessModel>());
      verify(mockApiClient.request(any, any, multipartData: anyNamed('multipartData'))).called(1);
    });

    test('success with PAN card (no back image)', () async {
      when(
        mockApiClient.request(any, any, multipartData: anyNamed('multipartData')),
      ).thenAnswer((_) async => <String, dynamic>{'success': true});

      final result = await repository.uploadbusinessKyc(
        userID: '1',
        documentType: 'PAN',
        documentNumber: '**********',
        nameOnPan: 'Test User',
        documentFrontImage: mockFileData,
        documentBackImage: null,
        isAddharCard: false,
        userType: 'business',
      );

      expect(result, isA<CommonSuccessModel>());
      verify(mockApiClient.request(any, any, multipartData: anyNamed('multipartData'))).called(1);
    });

    test('throws error', () async {
      when(
        mockApiClient.request(any, any, multipartData: anyNamed('multipartData')),
      ).thenThrow(Exception('Upload failed'));

      expect(
        () => repository.uploadbusinessKyc(
          userID: '1',
          documentType: 'PAN',
          documentNumber: '**********',
          nameOnPan: 'Test User',
          documentFrontImage: mockFileData,
          documentBackImage: null,
          isAddharCard: false,
          userType: 'business',
        ),
        throwsException,
      );
    });
  });

  group('generateCaptcha', () {
    test('success', () async {
      when(mockApiClient.request(any, any)).thenAnswer((_) async => <String, dynamic>{'captcha': '1234'});

      final result = await repository.generateCaptcha();

      expect(result, isA<CaptchaModel>());
      verify(mockApiClient.request(any, any)).called(1);
    });

    test('throws error', () async {
      when(mockApiClient.request(any, any)).thenThrow(Exception('Captcha generation failed'));

      expect(repository.generateCaptcha(), throwsException);
    });
  });

  group('reGenerateCaptcha', () {
    test('success', () async {
      when(mockApiClient.request(any, any)).thenAnswer(
        (_) async => {
          'timestamp': 1234567890,
          'transaction_id': 'txn123',
          'data': {'captcha': 'abcd'},
          'code': 200,
        },
      );

      final result = await repository.reGenerateCaptcha(sessionId: 'session123');

      expect(result, isA<RecaptchaModel>());
      verify(mockApiClient.request(any, any)).called(1);
    });

    test('throws error', () async {
      when(mockApiClient.request(any, any)).thenThrow(Exception('Recaptcha generation failed'));

      expect(repository.reGenerateCaptcha(sessionId: 'session123'), throwsException);
    });
  });

  group('generateAadharOTP', () {
    test('success', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenAnswer(
        (_) async => {
          'code': 200,
          'timestamp': 1234567890,
          'transaction_id': 'txn123',
          'sub_code': 'sub1',
          'message': 'OTP sent successfully',
        },
      );

      final result = await repository.generateAadharOTP(
        aadhaarNumber: '123412341234',
        captcha: 'abcd',
        sessionId: 'session123',
      );

      expect(result, isA<AadharOTPSendModel>());
      verify(mockApiClient.request(any, any, data: anyNamed('data'))).called(1);
    });

    test('throws error', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenThrow(Exception('OTP generation failed'));

      expect(
        repository.generateAadharOTP(aadhaarNumber: '123412341234', captcha: 'abcd', sessionId: 'session123'),
        throwsException,
      );
    });
  });

  group('validateAadharOtp', () {
    test('success', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenAnswer(
        (_) async => {
          'code': 200,
          'timestamp': 1234567890,
          'transaction_id': 'txn123',
          'sub_code': 'sub1',
          'message': 'OTP verified successfully',
          'data': {
            'address': {
              'care_of': 'c/o Test',
              'country': 'IN',
              'district': 'Test District',
              'house': '123',
              'landmark': 'Test Landmark',
              'locality': 'Test Locality',
              'pin': '123456',
              'post_office': 'Test PO',
              'state': 'Test State',
              'street': 'Test Street',
              'sub_district': 'Test SubDistrict',
              'vtc': 'Test VTC',
            },
            'date_of_birth': '1990-01-01',
            'email': '<EMAIL>',
            'gender': 'M',
            'generated_at': '2023-01-01T00:00:00Z',
            'masked_number': 'XXXX1234',
            'name': 'Test Name',
            'phone': '9999999999',
            'photo': 'base64encodedphoto',
          },
        },
      );

      final result = await repository.validateAadharOtp(
        faker: true,
        otp: '123456',
        sessionId: 'session123',
        userId: '1',
      );

      expect(result, isA<AadharOTPVerifyModel>());
      verify(mockApiClient.request(any, any, data: anyNamed('data'))).called(1);
    });

    test('throws error', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenThrow(Exception('OTP validation failed'));

      expect(
        repository.validateAadharOtp(faker: true, otp: '123456', sessionId: 'session123', userId: '1'),
        throwsException,
      );
    });
  });

  group('getGSTDetails', () {
    test('success', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenAnswer(
        (_) async => {
          'success': true,
          'data': {
            'legal_name': 'Test Business Company',
            'message': 'GST details retrieved successfully',
            'status': 'Active',
          },
        },
      );

      final result = await repository.getGSTDetails(
        userID: '1',
        estimatedAnnualIncome: '5000000',
        gstNumber: '12ABCDE3456F7Z8',
      );

      expect(result, isA<GetGstDetailsModel>());
      expect(result.success, isTrue);
      expect(result.data?.legalName, equals('Test Business Company'));
      expect(result.data?.message, equals('GST details retrieved successfully'));
      expect(result.data?.status, equals('Active'));

      verify(mockApiClient.request(any, any, data: anyNamed('data'))).called(1);
    });

    test('throws error', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenThrow(Exception('GST details fetch failed'));

      expect(
        repository.getGSTDetails(userID: '1', estimatedAnnualIncome: '5000000', gstNumber: '12ABCDE3456F7Z8'),
        throwsException,
      );
    });
  });

  group('uploadGSTDocument', () {
    test('success with GST certificate', () async {
      when(
        mockApiClient.request(any, any, multipartData: anyNamed('multipartData')),
      ).thenAnswer((_) async => <String, dynamic>{'success': true});

      final result = await repository.uploadGSTDocument(
        userID: '1',
        gstNumber: '12ABCDE3456F7Z8',
        userType: 'business',
        gstCertificate: mockFileData,
      );

      expect(result, isA<CommonSuccessModel>());
      verify(mockApiClient.request(any, any, multipartData: anyNamed('multipartData'))).called(1);
    });

    test('success without GST certificate', () async {
      when(
        mockApiClient.request(any, any, multipartData: anyNamed('multipartData')),
      ).thenAnswer((_) async => <String, dynamic>{'success': true});

      final result = await repository.uploadGSTDocument(
        userID: '1',
        gstNumber: '12ABCDE3456F7Z8',
        userType: 'business',
        gstCertificate: null,
      );

      expect(result, isA<CommonSuccessModel>());
      verify(mockApiClient.request(any, any, multipartData: anyNamed('multipartData'))).called(1);
    });

    test('throws error', () async {
      when(
        mockApiClient.request(any, any, multipartData: anyNamed('multipartData')),
      ).thenThrow(Exception('GST document upload failed'));

      expect(
        repository.uploadGSTDocument(
          userID: '1',
          gstNumber: '12ABCDE3456F7Z8',
          userType: 'business',
          gstCertificate: mockFileData,
        ),
        throwsException,
      );
    });
  });
}
