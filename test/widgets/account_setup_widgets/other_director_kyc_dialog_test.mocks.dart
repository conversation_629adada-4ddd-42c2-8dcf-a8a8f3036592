// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in exchek/test/widgets/account_setup_widgets/other_director_kyc_dialog_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:exchek/core/utils/exports.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeBusinessAccountSetupState_0 extends _i1.SmartFake
    implements _i2.BusinessAccountSetupState {
  _FakeBusinessAccountSetupState_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRouteConfiguration_1 extends _i1.SmartFake
    implements _i2.RouteConfiguration {
  _FakeRouteConfiguration_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBackButtonDispatcher_2 extends _i1.SmartFake
    implements _i2.BackButtonDispatcher {
  _FakeBackButtonDispatcher_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoRouterDelegate_3 extends _i1.SmartFake
    implements _i2.GoRouterDelegate {
  _FakeGoRouterDelegate_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoRouteInformationProvider_4 extends _i1.SmartFake
    implements _i2.GoRouteInformationProvider {
  _FakeGoRouteInformationProvider_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoRouteInformationParser_5 extends _i1.SmartFake
    implements _i2.GoRouteInformationParser {
  _FakeGoRouteInformationParser_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoRouterState_6 extends _i1.SmartFake implements _i2.GoRouterState {
  _FakeGoRouterState_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [BusinessAccountSetupBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockBusinessAccountSetupBloc extends _i1.Mock
    implements _i2.BusinessAccountSetupBloc {
  MockBusinessAccountSetupBloc() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.BusinessAccountSetupState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeBusinessAccountSetupState_0(
              this,
              Invocation.getter(#state),
            ),
          )
          as _i2.BusinessAccountSetupState);

  @override
  _i3.Stream<_i2.BusinessAccountSetupState> get stream =>
      (super.noSuchMethod(
            Invocation.getter(#stream),
            returnValue: _i3.Stream<_i2.BusinessAccountSetupState>.empty(),
          )
          as _i3.Stream<_i2.BusinessAccountSetupState>);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  _i3.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i2.FileData?> getFileDataFromPath(
    String? path,
    String? fallbackName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getFileDataFromPath, [path, fallbackName]),
            returnValue: _i3.Future<_i2.FileData?>.value(),
          )
          as _i3.Future<_i2.FileData?>);

  @override
  _i3.Future<_i2.FileData?> downloadFileDataFromUrl(
    String? url,
    String? name,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#downloadFileDataFromUrl, [url, name]),
            returnValue: _i3.Future<_i2.FileData?>.value(),
          )
          as _i3.Future<_i2.FileData?>);

  @override
  void add(_i2.BusinessAccountSetupEvent? event) => super.noSuchMethod(
    Invocation.method(#add, [event]),
    returnValueForMissingStub: null,
  );

  @override
  void onEvent(_i2.BusinessAccountSetupEvent? event) => super.noSuchMethod(
    Invocation.method(#onEvent, [event]),
    returnValueForMissingStub: null,
  );

  @override
  void emit(_i2.BusinessAccountSetupState? state) => super.noSuchMethod(
    Invocation.method(#emit, [state]),
    returnValueForMissingStub: null,
  );

  @override
  void on<E extends _i2.BusinessAccountSetupEvent>(
    _i2.EventHandler<E, _i2.BusinessAccountSetupState>? handler, {
    _i2.EventTransformer<E>? transformer,
  }) => super.noSuchMethod(
    Invocation.method(#on, [handler], {#transformer: transformer}),
    returnValueForMissingStub: null,
  );

  @override
  void onTransition(
    _i2.Transition<
      _i2.BusinessAccountSetupEvent,
      _i2.BusinessAccountSetupState
    >?
    transition,
  ) => super.noSuchMethod(
    Invocation.method(#onTransition, [transition]),
    returnValueForMissingStub: null,
  );

  @override
  void onChange(_i2.Change<_i2.BusinessAccountSetupState>? change) =>
      super.noSuchMethod(
        Invocation.method(#onChange, [change]),
        returnValueForMissingStub: null,
      );

  @override
  void addError(Object? error, [StackTrace? stackTrace]) => super.noSuchMethod(
    Invocation.method(#addError, [error, stackTrace]),
    returnValueForMissingStub: null,
  );

  @override
  void onError(Object? error, StackTrace? stackTrace) => super.noSuchMethod(
    Invocation.method(#onError, [error, stackTrace]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [GoRouter].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoRouter extends _i1.Mock implements _i2.GoRouter {
  MockGoRouter() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.RouteConfiguration get configuration =>
      (super.noSuchMethod(
            Invocation.getter(#configuration),
            returnValue: _FakeRouteConfiguration_1(
              this,
              Invocation.getter(#configuration),
            ),
          )
          as _i2.RouteConfiguration);

  @override
  _i2.BackButtonDispatcher get backButtonDispatcher =>
      (super.noSuchMethod(
            Invocation.getter(#backButtonDispatcher),
            returnValue: _FakeBackButtonDispatcher_2(
              this,
              Invocation.getter(#backButtonDispatcher),
            ),
          )
          as _i2.BackButtonDispatcher);

  @override
  _i2.GoRouterDelegate get routerDelegate =>
      (super.noSuchMethod(
            Invocation.getter(#routerDelegate),
            returnValue: _FakeGoRouterDelegate_3(
              this,
              Invocation.getter(#routerDelegate),
            ),
          )
          as _i2.GoRouterDelegate);

  @override
  _i2.GoRouteInformationProvider get routeInformationProvider =>
      (super.noSuchMethod(
            Invocation.getter(#routeInformationProvider),
            returnValue: _FakeGoRouteInformationProvider_4(
              this,
              Invocation.getter(#routeInformationProvider),
            ),
          )
          as _i2.GoRouteInformationProvider);

  @override
  _i2.GoRouteInformationParser get routeInformationParser =>
      (super.noSuchMethod(
            Invocation.getter(#routeInformationParser),
            returnValue: _FakeGoRouteInformationParser_5(
              this,
              Invocation.getter(#routeInformationParser),
            ),
          )
          as _i2.GoRouteInformationParser);

  @override
  bool get overridePlatformDefaultLocation =>
      (super.noSuchMethod(
            Invocation.getter(#overridePlatformDefaultLocation),
            returnValue: false,
          )
          as bool);

  @override
  _i2.GoRouterState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeGoRouterState_6(this, Invocation.getter(#state)),
          )
          as _i2.GoRouterState);

  @override
  set configuration(_i2.RouteConfiguration? _configuration) =>
      super.noSuchMethod(
        Invocation.setter(#configuration, _configuration),
        returnValueForMissingStub: null,
      );

  @override
  set routerDelegate(_i2.GoRouterDelegate? _routerDelegate) =>
      super.noSuchMethod(
        Invocation.setter(#routerDelegate, _routerDelegate),
        returnValueForMissingStub: null,
      );

  @override
  set routeInformationProvider(
    _i2.GoRouteInformationProvider? _routeInformationProvider,
  ) => super.noSuchMethod(
    Invocation.setter(#routeInformationProvider, _routeInformationProvider),
    returnValueForMissingStub: null,
  );

  @override
  set routeInformationParser(
    _i2.GoRouteInformationParser? _routeInformationParser,
  ) => super.noSuchMethod(
    Invocation.setter(#routeInformationParser, _routeInformationParser),
    returnValueForMissingStub: null,
  );

  @override
  bool canPop() =>
      (super.noSuchMethod(Invocation.method(#canPop, []), returnValue: false)
          as bool);

  @override
  String namedLocation(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    String? fragment,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #namedLocation,
              [name],
              {
                #pathParameters: pathParameters,
                #queryParameters: queryParameters,
                #fragment: fragment,
              },
            ),
            returnValue: _i4.dummyValue<String>(
              this,
              Invocation.method(
                #namedLocation,
                [name],
                {
                  #pathParameters: pathParameters,
                  #queryParameters: queryParameters,
                  #fragment: fragment,
                },
              ),
            ),
          )
          as String);

  @override
  void go(String? location, {Object? extra}) => super.noSuchMethod(
    Invocation.method(#go, [location], {#extra: extra}),
    returnValueForMissingStub: null,
  );

  @override
  void restore(_i2.RouteMatchList? matchList) => super.noSuchMethod(
    Invocation.method(#restore, [matchList]),
    returnValueForMissingStub: null,
  );

  @override
  void goNamed(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
    String? fragment,
  }) => super.noSuchMethod(
    Invocation.method(
      #goNamed,
      [name],
      {
        #pathParameters: pathParameters,
        #queryParameters: queryParameters,
        #extra: extra,
        #fragment: fragment,
      },
    ),
    returnValueForMissingStub: null,
  );

  @override
  _i3.Future<T?> push<T extends Object?>(String? location, {Object? extra}) =>
      (super.noSuchMethod(
            Invocation.method(#push, [location], {#extra: extra}),
            returnValue: _i3.Future<T?>.value(),
          )
          as _i3.Future<T?>);

  @override
  _i3.Future<T?> pushNamed<T extends Object?>(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #pushNamed,
              [name],
              {
                #pathParameters: pathParameters,
                #queryParameters: queryParameters,
                #extra: extra,
              },
            ),
            returnValue: _i3.Future<T?>.value(),
          )
          as _i3.Future<T?>);

  @override
  _i3.Future<T?> pushReplacement<T extends Object?>(
    String? location, {
    Object? extra,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#pushReplacement, [location], {#extra: extra}),
            returnValue: _i3.Future<T?>.value(),
          )
          as _i3.Future<T?>);

  @override
  _i3.Future<T?> pushReplacementNamed<T extends Object?>(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #pushReplacementNamed,
              [name],
              {
                #pathParameters: pathParameters,
                #queryParameters: queryParameters,
                #extra: extra,
              },
            ),
            returnValue: _i3.Future<T?>.value(),
          )
          as _i3.Future<T?>);

  @override
  _i3.Future<T?> replace<T>(String? location, {Object? extra}) =>
      (super.noSuchMethod(
            Invocation.method(#replace, [location], {#extra: extra}),
            returnValue: _i3.Future<T?>.value(),
          )
          as _i3.Future<T?>);

  @override
  _i3.Future<T?> replaceNamed<T>(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #replaceNamed,
              [name],
              {
                #pathParameters: pathParameters,
                #queryParameters: queryParameters,
                #extra: extra,
              },
            ),
            returnValue: _i3.Future<T?>.value(),
          )
          as _i3.Future<T?>);

  @override
  void pop<T extends Object?>([T? result]) => super.noSuchMethod(
    Invocation.method(#pop, [result]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}
