import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:exchek/core/utils/local_storage.dart';

void main() {
  // In-memory storage for testing
  final Map<String, String> mockStorage = {};

  setUpAll(() {
    // Initialize Flutter binding for tests
    TestWidgetsFlutterBinding.ensureInitialized();

    // Mock flutter_secure_storage plugin
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'write':
            final String key = methodCall.arguments['key'];
            final String value = methodCall.arguments['value'];
            mockStorage[key] = value;
            return null;
          case 'read':
            final String key = methodCall.arguments['key'];
            return mockStorage[key];
          case 'delete':
            final String key = methodCall.arguments['key'];
            mockStorage.remove(key);
            return null;
          case 'deleteAll':
            mockStorage.clear();
            return null;
          case 'readAll':
            return Map<String, String>.from(mockStorage);
          case 'containsKey':
            final String key = methodCall.arguments['key'];
            return mockStorage.containsKey(key);
          default:
            return null;
        }
      },
    );

    // Load test environment variables
    dotenv.testLoad(
      fileInput: '''
ENCRYPT_KEY=1234567890123456
ENCRYPT_IV=1234567890123456
''',
    );
  });

  tearDownAll(() {
    // Clean up the mock method channel handler
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
      null,
    );
  });

  group('LocalStorage', () {
    late LocalStorage localStorage;

    setUp(() {
      // Clear mock storage between tests
      mockStorage.clear();
      // Reset the singleton instance for each test
      localStorage = LocalStorage();
    });

    // =============================================================================
    // SINGLETON PATTERN TESTS
    // =============================================================================

    group('Singleton Pattern', () {
      test('should return the same instance when called multiple times', () {
        // Arrange & Act
        final instance1 = LocalStorage();
        final instance2 = LocalStorage();
        final instance3 = LocalStorage();

        // Assert
        expect(identical(instance1, instance2), isTrue);
        expect(identical(instance2, instance3), isTrue);
        expect(identical(instance1, instance3), isTrue);
      });

      test('should maintain state across multiple calls', () {
        // Arrange & Act
        final instance1 = LocalStorage();
        final instance2 = LocalStorage();

        // Assert - Both instances should be the same object
        expect(instance1, equals(instance2));
        expect(instance1.hashCode, equals(instance2.hashCode));
      });

      test('should initialize FlutterSecureStorage with correct options', () {
        // Arrange & Act
        final localStorage = LocalStorage();

        // Assert - Instance should be created successfully
        expect(localStorage, isNotNull);
        expect(localStorage, isA<LocalStorage>());
      });

      test('should have static encryption fields initialized', () {
        // This test ensures the static fields are accessed and initialized
        // which improves coverage of the static field declarations

        // Act - Create instance which will initialize static fields
        final localStorage = LocalStorage();

        // Assert - Instance should be created without errors
        expect(localStorage, isNotNull);

        // The static fields (_key, _iv, _encrypter) are private but their
        // initialization is covered by creating the instance
      });
    });

    // =============================================================================
    // INITIALIZATION TESTS
    // =============================================================================

    group('Initialization', () {
      test('should initialize without throwing exceptions', () {
        // Act & Assert
        expect(() => LocalStorage(), returnsNormally);
      });

      test('should handle web preferences initialization', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act & Assert
        expect(() async => await localStorage.initWebPreferences(), returnsNormally);
      });

      test('should not reinitialize web preferences if already initialized', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        await localStorage.initWebPreferences();

        // Act - Call again
        await localStorage.initWebPreferences();

        // Assert - Should not throw and should complete normally
        expect(localStorage, isNotNull);
      });
    });

    // =============================================================================
    // PUT METHOD TESTS
    // =============================================================================

    group('put method', () {
      test('should store value successfully', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'test_key';
        const value = 'test_value';

        // Act & Assert
        expect(() async => await localStorage.put(key, value), returnsNormally);
      });

      test('should handle empty key', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = '';
        const value = 'test_value';

        // Act & Assert
        expect(() async => await localStorage.put(key, value), returnsNormally);
      });

      test('should handle empty value', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'test_key';
        const value = '';

        // Act & Assert
        expect(() async => await localStorage.put(key, value), returnsNormally);
      });

      test('should handle special characters in key and value', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'test_key_@#\$%^&*()';
        const value = 'test_value_@#\$%^&*()_with_special_chars';

        // Act & Assert
        expect(() async => await localStorage.put(key, value), returnsNormally);
      });

      test('should handle unicode characters', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'test_key_🔑';
        const value = 'test_value_🌟_unicode_text_中文_العربية';

        // Act & Assert
        expect(() async => await localStorage.put(key, value), returnsNormally);
      });

      test('should handle very long strings', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'test_key';
        final value = 'a' * 10000; // Very long string

        // Act & Assert
        expect(() async => await localStorage.put(key, value), returnsNormally);
      });
    });

    // =============================================================================
    // GET METHOD TESTS
    // =============================================================================

    group('get method', () {
      test('should retrieve stored value successfully', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'test_key';
        const value = 'test_value';

        // Act
        await localStorage.put(key, value);
        final result = await localStorage.get(key);

        // Assert
        expect(result, equals(value));
      });

      test('should return null for non-existent key', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'non_existent_key';

        // Act
        final result = await localStorage.get(key);

        // Assert
        expect(result, isNull);
      });

      test('should handle empty key', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = '';

        // Act
        final result = await localStorage.get(key);

        // Assert
        expect(result, isNull);
      });

      test('should retrieve empty value correctly', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'empty_key';
        const value = '';

        // Act
        await localStorage.put(key, value);
        final result = await localStorage.get(key);

        // Assert
        expect(result, equals(value));
      });

      test('should handle special characters in retrieved value', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'special_key';
        const value = 'special_value_@#\$%^&*()';

        // Act
        await localStorage.put(key, value);
        final result = await localStorage.get(key);

        // Assert
        expect(result, equals(value));
      });

      test('should handle unicode characters in retrieved value', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'unicode_key';
        const value = 'unicode_value_🌟_中文_العربية';

        // Act
        await localStorage.put(key, value);
        final result = await localStorage.get(key);

        // Assert
        expect(result, equals(value));
      });
    });

    // =============================================================================
    // DELETE METHOD TESTS
    // =============================================================================

    group('delete method', () {
      test('should delete existing key successfully', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'test_key';
        const value = 'test_value';

        // Act
        await localStorage.put(key, value);
        await localStorage.delete(key);
        final result = await localStorage.get(key);

        // Assert
        expect(result, isNull);
      });

      test('should handle deletion of non-existent key', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'non_existent_key';

        // Act & Assert
        expect(() async => await localStorage.delete(key), returnsNormally);
      });

      test('should handle empty key deletion', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = '';

        // Act & Assert
        expect(() async => await localStorage.delete(key), returnsNormally);
      });
    });

    // =============================================================================
    // DELETE ALL METHOD TESTS
    // =============================================================================

    group('deleteAll method', () {
      test('should delete all stored values', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key1 = 'key1';
        const value1 = 'value1';
        const key2 = 'key2';
        const value2 = 'value2';

        // Act
        await localStorage.put(key1, value1);
        await localStorage.put(key2, value2);
        await localStorage.deleteAll();

        final result1 = await localStorage.get(key1);
        final result2 = await localStorage.get(key2);

        // Assert
        expect(result1, isNull);
        expect(result2, isNull);
      });

      test('should handle deleteAll on empty storage', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act & Assert
        expect(() async => await localStorage.deleteAll(), returnsNormally);
      });
    });

    // =============================================================================
    // CONTAINS KEY METHOD TESTS
    // =============================================================================

    group('containsKey method', () {
      test('should return true for existing key', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'existing_key';
        const value = 'test_value';

        // Act
        await localStorage.put(key, value);
        final result = await localStorage.containsKey(key);

        // Assert
        expect(result, isTrue);
      });

      test('should return false for non-existent key', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'non_existent_key';

        // Act
        final result = await localStorage.containsKey(key);

        // Assert
        expect(result, isFalse);
      });

      test('should return false for empty key', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = '';

        // Act
        final result = await localStorage.containsKey(key);

        // Assert
        expect(result, isFalse);
      });

      test('should return false after key deletion', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'temp_key';
        const value = 'temp_value';

        // Act
        await localStorage.put(key, value);
        await localStorage.delete(key);
        final result = await localStorage.containsKey(key);

        // Assert
        expect(result, isFalse);
      });
    });

    // =============================================================================
    // READ ALL METHOD TESTS
    // =============================================================================

    group('readAll method', () {
      test('should return all stored key-value pairs', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key1 = 'key1';
        const value1 = 'value1';
        const key2 = 'key2';
        const value2 = 'value2';

        // Act
        await localStorage.put(key1, value1);
        await localStorage.put(key2, value2);
        final result = await localStorage.readAll();

        // Assert
        expect(result, isA<Map<String, String>>());
        expect(result.containsKey(key1), isTrue);
        expect(result.containsKey(key2), isTrue);
        expect(result[key1], equals(value1));
        expect(result[key2], equals(value2));
      });

      test('should return empty map when no data stored', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act
        final result = await localStorage.readAll();

        // Assert
        expect(result, isA<Map<String, String>>());
        expect(result.isEmpty, isTrue);
      });

      test('should handle mixed data types gracefully', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key1 = 'string_key';
        const value1 = 'string_value';
        const key2 = 'number_key';
        const value2 = '12345';

        // Act
        await localStorage.put(key1, value1);
        await localStorage.put(key2, value2);
        final result = await localStorage.readAll();

        // Assert
        expect(result, isA<Map<String, String>>());
        expect(result[key1], equals(value1));
        expect(result[key2], equals(value2));
      });
    });

    // =============================================================================
    // ERROR HANDLING TESTS
    // =============================================================================

    group('Error Handling', () {
      test('should handle put operation errors gracefully', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'test_key';
        const value = 'test_value';

        // Act & Assert - Should not throw even if there are internal errors
        expect(() async => await localStorage.put(key, value), returnsNormally);
      });

      test('should return null on get operation errors', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'error_key';

        // Act
        final result = await localStorage.get(key);

        // Assert
        expect(result, isNull);
      });

      test('should handle delete operation errors gracefully', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'test_key';

        // Act & Assert
        expect(() async => await localStorage.delete(key), returnsNormally);
      });

      test('should handle deleteAll operation errors gracefully', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act & Assert
        expect(() async => await localStorage.deleteAll(), returnsNormally);
      });

      test('should return false on containsKey operation errors', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'error_key';

        // Act
        final result = await localStorage.containsKey(key);

        // Assert
        expect(result, isFalse);
      });

      test('should return empty map on readAll operation errors', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act
        final result = await localStorage.readAll();

        // Assert
        expect(result, isA<Map<String, String>>());
      });
    });

    // =============================================================================
    // INTEGRATION TESTS
    // =============================================================================

    group('Integration Tests', () {
      test('should handle complete workflow: put, get, delete', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'workflow_key';
        const value = 'workflow_value';

        // Act & Assert
        // 1. Store value
        await localStorage.put(key, value);
        expect(await localStorage.containsKey(key), isTrue);

        // 2. Retrieve value
        final retrievedValue = await localStorage.get(key);
        expect(retrievedValue, equals(value));

        // 3. Delete value
        await localStorage.delete(key);
        expect(await localStorage.containsKey(key), isFalse);
        expect(await localStorage.get(key), isNull);
      });

      test('should handle multiple keys operations', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        final testData = {'key1': 'value1', 'key2': 'value2', 'key3': 'value3', 'key4': 'value4', 'key5': 'value5'};

        // Act
        // Store all values
        for (final entry in testData.entries) {
          await localStorage.put(entry.key, entry.value);
        }

        // Verify all values
        for (final entry in testData.entries) {
          final result = await localStorage.get(entry.key);
          expect(result, equals(entry.value));
          expect(await localStorage.containsKey(entry.key), isTrue);
        }

        // Read all values
        final allValues = await localStorage.readAll();
        expect(allValues.length, greaterThanOrEqualTo(testData.length));

        // Delete all values
        await localStorage.deleteAll();

        // Verify all values are deleted
        for (final key in testData.keys) {
          expect(await localStorage.get(key), isNull);
          expect(await localStorage.containsKey(key), isFalse);
        }
      });

      test('should handle overwriting existing values', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});
        const key = 'overwrite_key';
        const originalValue = 'original_value';
        const newValue = 'new_value';

        // Act
        await localStorage.put(key, originalValue);
        expect(await localStorage.get(key), equals(originalValue));

        await localStorage.put(key, newValue);
        final result = await localStorage.get(key);

        // Assert
        expect(result, equals(newValue));
        expect(result, isNot(equals(originalValue)));
      });
    });
  });

  // =============================================================================
  // ADVANCED ERROR HANDLING TESTS
  // =============================================================================

  group('Advanced Error Handling', () {
    late LocalStorage localStorage;

    setUp(() {
      mockStorage.clear();
      localStorage = LocalStorage();
    });

    test('should handle FlutterSecureStorage write exceptions', () async {
      // Arrange - Mock storage to throw exception on write
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'write') {
            throw PlatformException(code: 'WRITE_ERROR', message: 'Failed to write');
          }
          return null;
        },
      );

      // Act & Assert
      expect(() async => await localStorage.put('test_key', 'test_value'), throwsA(isA<PlatformException>()));

      // Restore original mock
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'write':
              final String key = methodCall.arguments['key'];
              final String value = methodCall.arguments['value'];
              mockStorage[key] = value;
              return null;
            case 'read':
              final String key = methodCall.arguments['key'];
              return mockStorage[key];
            case 'delete':
              final String key = methodCall.arguments['key'];
              mockStorage.remove(key);
              return null;
            case 'deleteAll':
              mockStorage.clear();
              return null;
            case 'readAll':
              return Map<String, String>.from(mockStorage);
            case 'containsKey':
              final String key = methodCall.arguments['key'];
              return mockStorage.containsKey(key);
            default:
              return null;
          }
        },
      );
    });

    test('should handle FlutterSecureStorage delete exceptions', () async {
      // Arrange - Mock storage to throw exception on delete
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'delete') {
            throw PlatformException(code: 'DELETE_ERROR', message: 'Failed to delete');
          }
          return null;
        },
      );

      // Act & Assert
      expect(() async => await localStorage.delete('test_key'), throwsA(isA<PlatformException>()));

      // Restore original mock
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'write':
              final String key = methodCall.arguments['key'];
              final String value = methodCall.arguments['value'];
              mockStorage[key] = value;
              return null;
            case 'read':
              final String key = methodCall.arguments['key'];
              return mockStorage[key];
            case 'delete':
              final String key = methodCall.arguments['key'];
              mockStorage.remove(key);
              return null;
            case 'deleteAll':
              mockStorage.clear();
              return null;
            case 'readAll':
              return Map<String, String>.from(mockStorage);
            case 'containsKey':
              final String key = methodCall.arguments['key'];
              return mockStorage.containsKey(key);
            default:
              return null;
          }
        },
      );
    });

    test('should handle FlutterSecureStorage deleteAll exceptions', () async {
      // Arrange - Mock storage to throw exception on deleteAll
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'deleteAll') {
            throw PlatformException(code: 'DELETE_ALL_ERROR', message: 'Failed to delete all');
          }
          return null;
        },
      );

      // Act & Assert
      expect(() async => await localStorage.deleteAll(), throwsA(isA<PlatformException>()));

      // Restore original mock
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'write':
              final String key = methodCall.arguments['key'];
              final String value = methodCall.arguments['value'];
              mockStorage[key] = value;
              return null;
            case 'read':
              final String key = methodCall.arguments['key'];
              return mockStorage[key];
            case 'delete':
              final String key = methodCall.arguments['key'];
              mockStorage.remove(key);
              return null;
            case 'deleteAll':
              mockStorage.clear();
              return null;
            case 'readAll':
              return Map<String, String>.from(mockStorage);
            case 'containsKey':
              final String key = methodCall.arguments['key'];
              return mockStorage.containsKey(key);
            default:
              return null;
          }
        },
      );
    });
  });

  // =============================================================================
  // EDGE CASE TESTS
  // =============================================================================

  group('Edge Cases', () {
    late LocalStorage localStorage;

    setUp(() {
      mockStorage.clear();
      localStorage = LocalStorage();
    });

    test('should handle null values from FlutterSecureStorage', () async {
      // Arrange - Mock storage to return null
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          return null; // Always return null
        },
      );

      // Act
      final result = await localStorage.get('any_key');
      final containsKey = await localStorage.containsKey('any_key');
      final readAll = await localStorage.readAll();

      // Assert
      expect(result, isNull);
      expect(containsKey, isFalse);
      expect(readAll, isEmpty);

      // Restore original mock
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'write':
              final String key = methodCall.arguments['key'];
              final String value = methodCall.arguments['value'];
              mockStorage[key] = value;
              return null;
            case 'read':
              final String key = methodCall.arguments['key'];
              return mockStorage[key];
            case 'delete':
              final String key = methodCall.arguments['key'];
              mockStorage.remove(key);
              return null;
            case 'deleteAll':
              mockStorage.clear();
              return null;
            case 'readAll':
              return Map<String, String>.from(mockStorage);
            case 'containsKey':
              final String key = methodCall.arguments['key'];
              return mockStorage.containsKey(key);
            default:
              return null;
          }
        },
      );
    });

    test('should handle extremely large data', () async {
      // Arrange
      const key = 'large_data_key';
      final largeValue = 'x' * 1000000; // 1MB of data

      // Act & Assert
      await localStorage.put(key, largeValue);
      final result = await localStorage.get(key);
      expect(result, equals(largeValue));

      // Cleanup
      await localStorage.delete(key);
    });

    test('should handle concurrent operations', () async {
      // Arrange
      const keyPrefix = 'concurrent_key_';
      const valuePrefix = 'concurrent_value_';
      final futures = <Future>[];

      // Act - Perform multiple concurrent operations
      for (int i = 0; i < 10; i++) {
        futures.add(localStorage.put('$keyPrefix$i', '$valuePrefix$i'));
      }

      await Future.wait(futures);

      // Assert - All values should be stored correctly
      for (int i = 0; i < 10; i++) {
        final result = await localStorage.get('$keyPrefix$i');
        expect(result, equals('$valuePrefix$i'));
      }

      // Cleanup
      await localStorage.deleteAll();
    });

    test('should handle keys with various character encodings', () async {
      // Arrange
      final testCases = {
        'ascii_key': 'ascii_value',
        'key_with_spaces and symbols !@#\$%^&*()': 'value with spaces',
        'key_with_newlines\n\r\t': 'value\nwith\nnewlines',
        'emoji_key_🔑': 'emoji_value_🌟',
        'chinese_key_中文': 'chinese_value_测试',
        'arabic_key_العربية': 'arabic_value_اختبار',
        'russian_key_русский': 'russian_value_тест',
        'japanese_key_日本語': 'japanese_value_テスト',
      };

      // Act & Assert
      for (final entry in testCases.entries) {
        await localStorage.put(entry.key, entry.value);
        final result = await localStorage.get(entry.key);
        expect(result, equals(entry.value), reason: 'Failed for key: ${entry.key}');
      }

      // Verify all keys exist
      final allData = await localStorage.readAll();
      expect(allData.length, equals(testCases.length));

      for (final entry in testCases.entries) {
        expect(allData, containsPair(entry.key, entry.value));
      }

      // Cleanup
      await localStorage.deleteAll();
    });

    test('should handle boundary value testing', () async {
      // Test empty strings
      await localStorage.put('', '');
      expect(await localStorage.get(''), equals(''));

      // Test single character
      await localStorage.put('a', 'b');
      expect(await localStorage.get('a'), equals('b'));

      // Test maximum typical string length
      final maxKey = 'k' * 255;
      final maxValue = 'v' * 10000;
      await localStorage.put(maxKey, maxValue);
      expect(await localStorage.get(maxKey), equals(maxValue));

      // Test special characters
      const specialChars = r'!@#$%^&*()_+-=[]{}|;:,.<>?`~';
      await localStorage.put(specialChars, specialChars);
      expect(await localStorage.get(specialChars), equals(specialChars));

      // Cleanup
      await localStorage.deleteAll();
    });

    test('should handle readAll with empty storage', () async {
      // Ensure storage is empty
      await localStorage.deleteAll();

      // Act
      final result = await localStorage.readAll();

      // Assert
      expect(result, isEmpty);
      expect(result, isA<Map<String, String>>());
    });

    test('should handle containsKey with various key types', () async {
      // Test with normal key
      await localStorage.put('normal_key', 'value');
      expect(await localStorage.containsKey('normal_key'), isTrue);
      expect(await localStorage.containsKey('non_existent'), isFalse);

      // Test with empty key
      await localStorage.put('', 'empty_key_value');
      expect(await localStorage.containsKey(''), isTrue);

      // Test with special characters
      const specialKey = r'key_with_!@#$%^&*()';
      await localStorage.put(specialKey, 'special_value');
      expect(await localStorage.containsKey(specialKey), isTrue);

      // Cleanup
      await localStorage.deleteAll();
    });
  });

  // =============================================================================
  // CONSTRUCTOR AND STATIC FIELD TESTS
  // =============================================================================

  group('Constructor and Static Fields', () {
    test('should access static encryption fields', () {
      // This test ensures static fields are initialized and accessed
      // which improves coverage of static field declarations

      // Create multiple instances to ensure static fields are properly shared
      final instance1 = LocalStorage();
      final instance2 = LocalStorage();
      final instance3 = LocalStorage();

      // All instances should be identical (singleton pattern)
      expect(identical(instance1, instance2), isTrue);
      expect(identical(instance2, instance3), isTrue);

      // Verify instances are not null and properly initialized
      expect(instance1, isNotNull);
      expect(instance2, isNotNull);
      expect(instance3, isNotNull);
    });

    test('should handle constructor initialization for non-web platform', () {
      // This test covers the constructor path for non-web platforms
      // The constructor initializes FlutterSecureStorage with specific options

      final localStorage = LocalStorage();

      // Verify the instance is properly created
      expect(localStorage, isA<LocalStorage>());
      expect(localStorage, isNotNull);

      // The constructor should have initialized the _storage field
      // (we can't directly test private fields, but we can test functionality)
    });
  });

  // =============================================================================
  // COMPREHENSIVE COVERAGE TESTS
  // =============================================================================

  group('Comprehensive Coverage', () {
    late LocalStorage localStorage;

    setUp(() {
      mockStorage.clear();
      localStorage = LocalStorage();
    });

    test('should cover all method signatures and return types', () async {
      // Test all public methods to ensure they're covered

      // Test put method signature
      await localStorage.put('signature_test', 'value');

      // Test get method signature and return type
      final getString = await localStorage.get('signature_test');
      expect(getString, isA<String?>());
      expect(getString, equals('value'));

      // Test delete method signature
      await localStorage.delete('signature_test');

      // Test deleteAll method signature
      await localStorage.deleteAll();

      // Test containsKey method signature and return type
      final containsResult = await localStorage.containsKey('any_key');
      expect(containsResult, isA<bool>());

      // Test readAll method signature and return type
      final readAllResult = await localStorage.readAll();
      expect(readAllResult, isA<Map<String, String>>());

      // Test initWebPreferences method signature
      await localStorage.initWebPreferences();
    });

    test('should handle method chaining and state consistency', () async {
      // Test method chaining and state consistency
      const key1 = 'chain_key1';
      const key2 = 'chain_key2';
      const value1 = 'chain_value1';
      const value2 = 'chain_value2';

      // Chain multiple operations
      await localStorage.put(key1, value1);
      await localStorage.put(key2, value2);

      expect(await localStorage.containsKey(key1), isTrue);
      expect(await localStorage.containsKey(key2), isTrue);

      final allData = await localStorage.readAll();
      expect(allData.length, equals(2));

      await localStorage.delete(key1);
      expect(await localStorage.containsKey(key1), isFalse);
      expect(await localStorage.containsKey(key2), isTrue);

      await localStorage.deleteAll();
      expect(await localStorage.containsKey(key2), isFalse);
    });

    test('should handle all error paths and exception types', () async {
      // Test various error scenarios to improve coverage

      // Test with mock that returns different error types
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'read':
              throw Exception('Generic exception');
            case 'containsKey':
              throw ArgumentError('Argument error');
            case 'readAll':
              throw StateError('State error');
            default:
              return null;
          }
        },
      );

      // These should handle errors gracefully
      final readResult = await localStorage.get('error_key');
      expect(readResult, isNull);

      final containsResult = await localStorage.containsKey('error_key');
      expect(containsResult, isFalse);

      final readAllResult = await localStorage.readAll();
      expect(readAllResult, isEmpty);

      // Restore original mock
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'write':
              final String key = methodCall.arguments['key'];
              final String value = methodCall.arguments['value'];
              mockStorage[key] = value;
              return null;
            case 'read':
              final String key = methodCall.arguments['key'];
              return mockStorage[key];
            case 'delete':
              final String key = methodCall.arguments['key'];
              mockStorage.remove(key);
              return null;
            case 'deleteAll':
              mockStorage.clear();
              return null;
            case 'readAll':
              return Map<String, String>.from(mockStorage);
            case 'containsKey':
              final String key = methodCall.arguments['key'];
              return mockStorage.containsKey(key);
            default:
              return null;
          }
        },
      );
    });
  });

  // =============================================================================
  // PREFKEYS CLASS TESTS
  // =============================================================================

  group('Prefkeys', () {
    test('should have all required constant keys', () {
      // Assert - Verify all constants exist and have correct values
      expect(Prefkeys.lightDark, equals('light_dark'));
      expect(Prefkeys.followSystem, equals('follow_system'));
      expect(Prefkeys.authToken, equals('auth_token'));
      expect(Prefkeys.businessEntityType, equals('business_entity_type'));
      expect(Prefkeys.emailId, equals('email_id'));
      expect(Prefkeys.mobileNumber, equals('mobile_number'));
      expect(Prefkeys.exportsGood, equals('exports_good'));
      expect(Prefkeys.exportServices, equals('export_service'));
      expect(Prefkeys.freelancer, equals('freelancer'));
      expect(Prefkeys.user, equals('user'));
      expect(Prefkeys.resetPasswordToken, equals('reset_password_token'));
      expect(Prefkeys.verifyemailToken, equals('verify_email_token'));
      expect(Prefkeys.userId, equals('user_id'));
      expect(Prefkeys.loggedPhoneNumber, equals('logged_phone_number'));
      expect(Prefkeys.loggedUserName, equals('logged_user_name'));
      expect(Prefkeys.loggedEmail, equals('logged_email'));
      expect(Prefkeys.sessionId, equals('session_id'));
    });

    test('should have unique values for all keys', () {
      // Arrange
      final allKeys = [
        Prefkeys.lightDark,
        Prefkeys.followSystem,
        Prefkeys.authToken,
        Prefkeys.businessEntityType,
        Prefkeys.emailId,
        Prefkeys.mobileNumber,
        Prefkeys.exportsGood,
        Prefkeys.exportServices,
        Prefkeys.freelancer,
        Prefkeys.user,
        Prefkeys.resetPasswordToken,
        Prefkeys.verifyemailToken,
        Prefkeys.userId,
        Prefkeys.loggedPhoneNumber,
        Prefkeys.loggedUserName,
        Prefkeys.loggedEmail,
        Prefkeys.sessionId,
      ];

      // Act
      final uniqueKeys = allKeys.toSet();

      // Assert
      expect(uniqueKeys.length, equals(allKeys.length));
    });
  });

  // =============================================================================
  // PREFOBJ CLASS TESTS
  // =============================================================================

  group('Prefobj', () {
    test('should provide LocalStorage instance', () {
      // Act
      final preferences = Prefobj.preferences;

      // Assert
      expect(preferences, isA<LocalStorage>());
      expect(preferences, isNotNull);
    });

    test('should return same instance on multiple calls', () {
      // Act
      final preferences1 = Prefobj.preferences;
      final preferences2 = Prefobj.preferences;

      // Assert
      expect(identical(preferences1, preferences2), isTrue);
    });

    test('should allow storage operations through Prefobj', () async {
      // Arrange
      const key = 'prefobj_test_key';
      const value = 'prefobj_test_value';

      // Act
      await Prefobj.preferences.put(key, value);
      final result = await Prefobj.preferences.get(key);

      // Assert
      expect(result, equals(value));
    });
  });
}
