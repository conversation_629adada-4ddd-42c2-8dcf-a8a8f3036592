import 'package:exchek/core/flavor_config/flavor_config.dart';

class ApiEndPoint {
  static String get baseUrl => FlavorConfig.instance.env.baseUrl;
  static String get ipUrl => 'https://api.ipify.org?format=json';

  static String get socialLoginUrl => '$baseUrl/auth/social-login';
  static String get emailavailability => '$baseUrl/api/v1/auth/email-availability';
  static String get mobileavailability => '$baseUrl/api/v1/auth/mobile-availability';
  static String get sendEmailVerificationLinkUrl => '$baseUrl/api/v1/email/send-email-verification-link';
  static String get sendOtpUrl => '$baseUrl/api/v1/whatsapp/send_otp';
  static String get validateLoginOtpUrl => '$baseUrl/api/v1/whatsapp/validate_login_otp';
  static String get validateRegistrationOtpUrl => '$baseUrl/api/v1/whatsapp/validate_registration_otp';
  static String get updatePasswordUrl => '$baseUrl/api/v1/auth/update-password';
  static String get getDropdownOptionUrl => '$baseUrl/api/v1/dropdown-options/get-user-service-options';
  static String get loginurl => '$baseUrl/api/v1/auth/login';
  static String get registerurl => '$baseUrl/api/v1/auth/register';
  static String get getCurrencyOptionUrl => '$baseUrl/api/v1/dropdown-options/get-currency-options';
  static String get validateForgotPasswordOtpUrl => '$baseUrl/api/v1/whatsapp/validate_forgot_otp';
  static String get logoutUrl => '$baseUrl/api/v1/auth/logout';
  static String get uploadPersonalKycUrl => '$baseUrl/api/v1/upload/personal-kyc';
  static String get generateCaptchaUrl => '$baseUrl/api/v1/aadhar/generate-captcha';
  static String get generateReCaptchaUrl => '$baseUrl/api/v1/aadhar/re-captcha';
  static String get generateAadharOTP => '$baseUrl/api/v1/aadhar/generate-otp';
  static String get validateAadharOTPUrl => '$baseUrl/api/v1/aadhar/verify-otp';
  static String get getPanDetailUrl => '$baseUrl/api/v1/aadhar/get-pan-details';
  static String get getCityAndStateUrl => '$baseUrl/api/v1/googleapi/get-city-and-state';
  static String get uploadResidentialAddressDetailsUrl => '$baseUrl/api/v1/upload/residential-address-details';
  static String get getUserDetailsUrl => '$baseUrl/api/v1/details/user/';
  static String get getGSTDetailsUrl => '$baseUrl/api/v1/gst/get-details';
  static String get gstDocumentUrl => '$baseUrl/api/v1/upload/gst-document';
  static String get businessLegalDocuments => '$baseUrl/api/v1/upload/business-legal-documents';
  static String get verifyBankAccountUrl => '$baseUrl/api/v1/bank/verify';
  static String get uploadBankDocumentUrl => '$baseUrl/api/v1/upload/bank-document';
  static String get presignedUrl => '$baseUrl/api/v1/upload/presigned-url';
}
