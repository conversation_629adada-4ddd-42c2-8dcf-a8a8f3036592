import 'dart:convert';

import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/views/account_setup_view/business_account_setup_view/business_account_kyc_setup_view/company_pan_detail.dart';
import 'package:exchek/views/account_setup_view/business_account_setup_view/business_account_kyc_setup_view/contact_information.dart';
import 'package:exchek/views/account_setup_view/business_account_setup_view/business_account_kyc_setup_view/partner_aadhar_verification_view.dart';
import 'package:exchek/views/account_setup_view/business_account_setup_view/business_account_kyc_setup_view/proprietor_aadhar_verification_view.dart';
import 'package:exchek/widgets/common_widget/app_loader_widget.dart';

class EkycView extends StatelessWidget {
  final ScrollController _scrollController = ScrollController();

  EkycView({super.key});

  Future<void> _loadKyc(BuildContext context) async {
    context.read<BusinessAccountSetupBloc>().add(LoadBusinessKycFromLocal());
  }

  final Future<String?> _userFuture = Prefobj.preferences.get(Prefkeys.userDetail);

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future:
          (() async {
            await _loadKyc(context);
            return await Prefobj.preferences.get(Prefkeys.userDetail);
          })(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: AppLoaderWidget());
        }

        final userDetail = jsonDecode(snapshot.data!);
        final businessDetails = userDetail['business_details'];
        final businessType = businessDetails != null ? businessDetails['business_type'] : null;
        final steps =
            businessType == Lang.of(context).lbl_company
                ? [
                  KycVerificationSteps.aadharVerfication,
                  KycVerificationSteps.companyIncorporationVerification,
                  KycVerificationSteps.annualTurnoverDeclaration,
                  KycVerificationSteps.iecVerification,
                  KycVerificationSteps.panVerification,
                  KycVerificationSteps.contactInformation,
                  KycVerificationSteps.registeredOfficeAddress,
                  KycVerificationSteps.bankAccountLinking,
                ]
                : (businessType == Lang.of(context).lbl_llp || businessType == Lang.of(context).lbl_partnership_firm)
                ? KycVerificationSteps.values.where((step) => step != KycVerificationSteps.contactInformation).toList()
                : KycVerificationSteps.values
                    .where(
                      (step) =>
                          step != KycVerificationSteps.companyIncorporationVerification &&
                          step != KycVerificationSteps.contactInformation,
                    )
                    .toList();

        return ResponsiveScaffold(
          appBar: PreferredSize(
            preferredSize: Size.fromHeight(ResponsiveHelper.isWebAndIsNotMobile(context) ? 99 : 59),
            child: BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
              builder: (context, state) {
                KycVerificationSteps effectiveCurrentStep = state.currentKycVerificationStep;
                final isCINStepShow =
                    businessType == Lang.of(context).lbl_company ||
                    businessType == Lang.of(context).lbl_llp ||
                    businessType == Lang.of(context).lbl_partnership_firm;
                if (!isCINStepShow &&
                    state.currentKycVerificationStep == KycVerificationSteps.companyIncorporationVerification) {
                  final cinIndex = KycVerificationSteps.values.indexOf(
                    KycVerificationSteps.companyIncorporationVerification,
                  );
                  // Find the next valid step after IEC in the filtered steps list
                  final nextStep = steps.firstWhere(
                    (step) => KycVerificationSteps.values.indexOf(step) > cinIndex,
                    orElse: () => steps.first,
                  );
                  effectiveCurrentStep = nextStep;
                } else if (!steps.contains(state.currentKycVerificationStep)) {
                  // If current step is not in the filtered steps list, find the next valid step
                  final currentIndex = KycVerificationSteps.values.indexOf(state.currentKycVerificationStep);
                  final nextStep = steps.firstWhere(
                    (step) => KycVerificationSteps.values.indexOf(step) > currentIndex,
                    orElse: () => steps.first,
                  );
                  effectiveCurrentStep = nextStep;
                } else if (businessType == Lang.of(context).lbl_company) {
                  // For company, ensure we follow the custom step order
                  if (!steps.contains(effectiveCurrentStep)) {
                    effectiveCurrentStep = steps.first;
                  }
                }

                return ExchekAppBar(
                  appBarContext: context,
                  title: getStepTitle(
                    context: context,
                    step: effectiveCurrentStep,
                    state: state,
                    businessType: businessType,
                  ),
                  onBackPressed: () {
                    final bloc = context.read<BusinessAccountSetupBloc>();
                    final index = steps.indexOf(effectiveCurrentStep);
                    if (index > 0) {
                      bloc.add(NavigateToPreviousKycStep());
                    } else {
                      if (kIsWeb) {
                        GoRouter.of(context).go(RouteUri.proceedWithKycViewRoute);
                      } else {
                        GoRouter.of(context).pop();
                      }
                    }
                  },
                  showProfile: true,
                  userName: "",
                  email: '',
                  isBusinessUser: true,
                  onLogout: () {
                    // TODO: Implement logout logic
                  },
                );
              },
            ),
          ),
          body: BackgroundImage(
            imagePath: Assets.images.svgs.other.appBg.path,
            child: BlocListener<BusinessAccountSetupBloc, BusinessAccountSetupState>(
              listenWhen:
                  (previous, current) => previous.currentKycVerificationStep != current.currentKycVerificationStep,
              listener: (context, state) {
                if (_scrollController.hasClients) {
                  _scrollController.animateTo(0, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
                }
              },
              child: BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
                builder: (context, state) {
                  KycVerificationSteps effectiveCurrentStep = state.currentKycVerificationStep;
                  final isCINStepShow =
                      businessType == Lang.of(context).lbl_company ||
                      businessType == Lang.of(context).lbl_llp ||
                      businessType == Lang.of(context).lbl_partnership_firm;
                  if (!isCINStepShow &&
                      state.currentKycVerificationStep == KycVerificationSteps.companyIncorporationVerification) {
                    final cinIndex = KycVerificationSteps.values.indexOf(
                      KycVerificationSteps.companyIncorporationVerification,
                    );
                    // Find the next valid step after IEC in the filtered steps list
                    final nextStep = steps.firstWhere(
                      (step) => KycVerificationSteps.values.indexOf(step) > cinIndex,
                      orElse: () => steps.first,
                    );
                    effectiveCurrentStep = nextStep;
                  } else if (!steps.contains(state.currentKycVerificationStep)) {
                    // If current step is not in the filtered steps list, find the next valid step
                    final currentIndex = KycVerificationSteps.values.indexOf(state.currentKycVerificationStep);
                    final nextStep = steps.firstWhere(
                      (step) => KycVerificationSteps.values.indexOf(step) > currentIndex,
                      orElse: () => steps.first,
                    );
                    effectiveCurrentStep = nextStep;
                  } else if (businessType == Lang.of(context).lbl_company) {
                    // For company, ensure we follow the custom step order
                    if (!steps.contains(effectiveCurrentStep)) {
                      effectiveCurrentStep = steps.first;
                    }
                  }

                  if (state.isLocalDataLoading) {
                    return const Center(child: AppLoaderWidget());
                  }

                  return Column(
                    children: [
                      if (state.isekycCollapsed && ResponsiveHelper.isWebAndIsNotMobile(context))
                        StepperSlider<KycVerificationSteps>(
                          currentStep: effectiveCurrentStep,
                          steps: steps,
                          title: getStepTitle(
                            context: context,
                            step: effectiveCurrentStep,
                            state: state,
                            businessType: businessType,
                          ),
                          isShowTitle: true,
                          isFullSlider: true,
                        ),
                      if (!ResponsiveHelper.isWebAndIsNotMobile(context)) ...[
                        _buildStepSlider(context, state, effectiveCurrentStep, steps),
                        buildSizedBoxH(20.0),
                      ],
                      Expanded(
                        child: NotificationListener<ScrollNotification>(
                          onNotification: (scrollNotification) {
                            if (_scrollController.hasClients) {
                              final isCollapsed = scrollNotification.metrics.pixels > (83.0 - kToolbarHeight);
                              context.read<BusinessAccountSetupBloc>().add(
                                BusinessEkycAppBarCollapseChanged(isCollapsed),
                              );
                            }
                            return false;
                          },
                          child: CustomScrollView(
                            controller: _scrollController,
                            slivers: [
                              if (ResponsiveHelper.isWebAndIsNotMobile(context))
                                SliverAppBar(
                                  pinned: true,
                                  surfaceTintColor: Colors.transparent,
                                  expandedHeight: 120.0,
                                  collapsedHeight: 56.0,
                                  flexibleSpace: FlexibleSpaceBar(
                                    background: LayoutBuilder(
                                      builder: (context, constraints) {
                                        final isCollapsed = constraints.maxHeight <= kToolbarHeight + 10;
                                        return AnimatedSwitcher(
                                          duration: Duration(milliseconds: 200),
                                          child:
                                              isCollapsed
                                                  ? SizedBox()
                                                  : _buildStepSlider(context, state, effectiveCurrentStep, steps),
                                        );
                                      },
                                    ),
                                  ),
                                  backgroundColor: Colors.transparent,
                                  elevation: 0,
                                ),
                              ..._buildStepContentAsSlivers(context, state, effectiveCurrentStep, isCINStepShow, steps),
                            ],
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildStepContentAsSlivers(
    BuildContext context,
    BusinessAccountSetupState state,
    KycVerificationSteps effectiveCurrentStep,
    bool isCINStepShow,
    List<KycVerificationSteps> steps,
  ) {
    switch (effectiveCurrentStep) {
      case KycVerificationSteps.aadharVerfication:
        return [
          SliverToBoxAdapter(
            child: Stack(
              children: [_buildStepOne(state, context), _buildBackButton(context, state, steps, effectiveCurrentStep)],
            ),
          ),
        ];
      case KycVerificationSteps.panVerification:
        return [
          SliverToBoxAdapter(
            child: Stack(
              children: [_buildStepTwo(state, context), _buildBackButton(context, state, steps, effectiveCurrentStep)],
            ),
          ),
        ];
      case KycVerificationSteps.registeredOfficeAddress:
        return [
          SliverToBoxAdapter(
            child: Stack(
              children: [RegisterBusinessAddressView(), _buildBackButton(context, state, steps, effectiveCurrentStep)],
            ),
          ),
        ];
      case KycVerificationSteps.annualTurnoverDeclaration:
        return [
          SliverToBoxAdapter(
            child: Stack(
              children: [AnnualTurnoverView(), _buildBackButton(context, state, steps, effectiveCurrentStep)],
            ),
          ),
        ];
      case KycVerificationSteps.iecVerification:
        return [
          SliverToBoxAdapter(
            child: Stack(
              children: [IceVerificationView(), _buildBackButton(context, state, steps, effectiveCurrentStep)],
            ),
          ),
        ];
      case KycVerificationSteps.companyIncorporationVerification:
        if (!isCINStepShow) return [];
        return [
          SliverToBoxAdapter(
            child: Stack(
              children: [
                CompanyIncorporationVerificationView(),
                _buildBackButton(context, state, steps, effectiveCurrentStep),
              ],
            ),
          ),
        ];
      case KycVerificationSteps.bankAccountLinking:
        return [
          SliverToBoxAdapter(
            child: Stack(
              children: [BankAccountLinkingView(), _buildBackButton(context, state, steps, effectiveCurrentStep)],
            ),
          ),
        ];
      case KycVerificationSteps.contactInformation:
        if (!steps.contains(KycVerificationSteps.contactInformation)) return [];
        return [
          SliverToBoxAdapter(
            child: Stack(
              children: [ContactInformation(), _buildBackButton(context, state, steps, effectiveCurrentStep)],
            ),
          ),
        ];
    }
  }

  Widget _buildBackButton(
    BuildContext context,
    BusinessAccountSetupState state,
    List<KycVerificationSteps> steps,
    KycVerificationSteps effectiveCurrentStep,
  ) {
    if (ResponsiveHelper.isWebAndIsNotMobile(context)) {
      return Align(
        alignment: Alignment.topCenter,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: ResponsiveHelper.getMaxFormWidth(context) + (ResponsiveHelper.isDesktop(context) ? 80.0 : 0),
          ),
          padding: EdgeInsets.only(
            top: 25.0,
            left: ResponsiveHelper.isDesktop(context) ? 0 : 10.0,
            right: ResponsiveHelper.isDesktop(context) ? 0 : 10.0,
          ),
          alignment: Alignment.topLeft,
          child: CustomImageView(
            imagePath: Assets.images.svgs.icons.icArrowLeft.path,
            height: 24.0,
            onTap: () {
              // final bloc = context.read<BusinessAccountSetupBloc>();
              // bloc.add(NavigateToPreviousKycStep());
              final bloc = context.read<BusinessAccountSetupBloc>();
              final index = steps.indexOf(effectiveCurrentStep);
              if (index > 0) {
                bloc.add(NavigateToPreviousKycStep());
              } else {
                if (kIsWeb) {
                  GoRouter.of(context).go(RouteUri.proceedWithKycViewRoute);
                } else {
                  GoRouter.of(context).pop();
                }
              }
            },
          ),
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }

  Widget _buildStepSlider(
    BuildContext context,
    BusinessAccountSetupState state,
    KycVerificationSteps effectiveCurrentStep,
    List<KycVerificationSteps> steps,
  ) {
    return FutureBuilder(
      future: _userFuture,
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data == null) {
          return const SizedBox.shrink();
        }
        final userDetail = jsonDecode(snapshot.data!);
        final businessDetails = userDetail['business_details'];
        final businessType = businessDetails != null ? businessDetails['business_type'] : null;

        return Container(
          constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxSliderWidthWidth(context)),
          padding: EdgeInsets.symmetric(horizontal: ResponsiveHelper.isMobile(context) ? 20 : 0.0),
          child: Column(
            children: [
              buildSizedBoxH(kIsWeb ? 42.0 : 26.0),
              StepperSlider<KycVerificationSteps>(
                currentStep: effectiveCurrentStep,
                steps: steps,
                title: getStepTitle(
                  context: context,
                  step: effectiveCurrentStep,
                  state: state,
                  businessType: businessType,
                ),
                isShowTitle: !ResponsiveHelper.isWebAndIsNotMobile(context),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStepTwo(BusinessAccountSetupState state, BuildContext context) {
    return FutureBuilder(
      future: _userFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState != ConnectionState.done) {
          return Center(child: CircularProgressIndicator());
        }

        final userDetail = jsonDecode(snapshot.data!);
        final businessDetails = userDetail['business_details'];
        final businessType = businessDetails != null ? businessDetails['business_type'] : null;

        // if (businessType == Lang.of(context).lbl_huf) {
        //   return HufPanVerificationView();
        // } else {
        //   return PanDetailView();
        // }
        if (businessType == 'HUF (Hindu Undivided Family)') {
          return HufPanVerificationView();
        } else if (businessType == 'LLP (Limited Liability Partnership)' || businessType == 'Partnership Firm') {
          return PanDetailView();
        } else if (businessType == 'Sole Proprietorship') {
          return PanDetailView();
        } else {
          return PanDetailView();
        }
      },
    );
  }

  Widget _buildStepOne(BusinessAccountSetupState state, BuildContext context) {
    return FutureBuilder(
      future: _userFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState != ConnectionState.done) {
          return Center(child: CircularProgressIndicator());
        }
        if (!snapshot.hasData || snapshot.data == null) {
          return CompanyPanDetail();
        }
        final userDetail = jsonDecode(snapshot.data!);
        final businessDetails = userDetail['business_details'];
        final businessType = businessDetails != null ? businessDetails['business_type'] : null;

        if (businessType == 'HUF (Hindu Undivided Family)') {
          return KartaAadharVerificationView();
        } else if (businessType == 'LLP (Limited Liability Partnership)' || businessType == 'Partnership Firm') {
          return PartnerAadharVerificationView();
        } else if (businessType == 'Sole Proprietorship') {
          return ProprietorAadharVerificationView();
        } else {
          return CompanyPanDetail();
        }
      },
    );
  }

  String getStepTitle({
    required BuildContext context,
    required KycVerificationSteps step,
    required BusinessAccountSetupState state,
    required String businessType,
  }) {
    switch (step) {
      case KycVerificationSteps.aadharVerfication:
        if (businessType == 'HUF (Hindu Undivided Family)') {
          return "Karta Aadhaar Verification";
        } else if (businessType == 'LLP (Limited Liability Partnership)' || businessType == 'Partnership Firm') {
          return "Partner Aadhaar Verification";
        } else if (businessType == 'Sole Proprietorship') {
          return "Proprietor Aadhaar Verification";
        }
        return "Company PAN";
      case KycVerificationSteps.panVerification:
        return businessType == Lang.of(context).lbl_huf ? "HUF PAN Verification" : "Identification of Directors";
      case KycVerificationSteps.registeredOfficeAddress:
        return "Registered Office Address";
      case KycVerificationSteps.annualTurnoverDeclaration:
        return "Annual Turnover Declaration";
      case KycVerificationSteps.iecVerification:
        return "IEC number and certificate upload";
      case KycVerificationSteps.companyIncorporationVerification:
        // return "Company Incorporation Verification";
        if (businessType == Lang.of(context).lbl_llp) {
          return "LLP Registration Verification";
        } else if (businessType == Lang.of(context).lbl_partnership_firm) {
          return "Partnership Verification";
        }
        return "Company Incorporation Verification";
      case KycVerificationSteps.bankAccountLinking:
        return "Bank Account Linking";
      case KycVerificationSteps.contactInformation:
        return "Contact Information";
    }
  }
}
