import 'dart:convert';
import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/widgets/common_widget/comman_verified_info_box.dart';

class AnnualTurnoverView extends StatelessWidget {
  const AnnualTurnoverView({super.key});

  // Static cache to prevent future recreation
  static final Map<String, Future<List<String>>> _turnoverCache = {};

  List<String> moreTurnOverList(BuildContext context) => ["Less than ₹40 lakhs", "₹40 lakhs or more"];

  List<String> lessTurnOverList(BuildContext context) => ["Less than ₹20 lakhs", "₹20 lakhs or more"];

  Future<List<String>> getTurnoverList(BuildContext context) async {
    final user = await Prefobj.preferences.get(Prefkeys.userDetail);
    final userDetail = jsonDecode(user!);

    final businessNature = userDetail['business_details']['business_nature'];

    if (businessNature == "Export of goods") {
      return moreTurnOverList(context);
    } else if (businessNature == "Export of services") {
      return lessTurnOverList(context);
    } else if (businessNature == "Export of goods and services") {
      return moreTurnOverList(context);
    } else {
      return lessTurnOverList(context);
    }
  }

  Future<List<String>> _getCachedTurnoverList(BuildContext context) {
    // Use a simple cache key - you could make this more sophisticated
    const cacheKey = 'turnover_list';

    if (!_turnoverCache.containsKey(cacheKey)) {
      _turnoverCache[cacheKey] = getTurnoverList(context);
    }

    return _turnoverCache[cacheKey]!;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
          child: SingleChildScrollView(
            padding: EdgeInsets.only(bottom: ResponsiveHelper.isWebAndIsNotMobile(context) ? 50 : 20),
            child: Center(
              child: Container(
                constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxTileWidth(context)),
                padding: EdgeInsetsGeometry.symmetric(
                  horizontal: ResponsiveHelper.isMobile(context) ? (kIsWeb ? 30.0 : 20) : 0.0,
                ),
                child: Form(
                  key: state.annualTurnoverFormKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildSizedBoxH(20.0),
                      _buildSelectionTitleAndDescription(
                        context: context,
                        title: Lang.of(context).lbl_turnover_previous_financial_year,
                        description: Lang.of(context).lbl_turnover_info_content,
                      ),
                      buildSizedBoxH(20.0),
                      _buildAnnualTurnOverOptions(context, state),
                      if (state.selectedAnnualTurnover != null) ...[
                        if (state.isGstCertificateMandatory == true) ...[
                          buildSizedBoxH(30.0),
                          _buildGstTitleAndDescription(context, state),
                          buildSizedBoxH(30.0),
                          _buildGSTNumberField(context, state),
                          if (!state.isGSTNumberVerify) ...[buildSizedBoxH(30.0), _buildVerifyButton()],
                          if (state.isGSTNumberVerify) ...[
                            buildSizedBoxH(24.0),
                            _buildVerifyGSTNameField(context, state),
                            buildSizedBoxH(24.0),
                            _buildUploadGSTCertificate(state, context),
                            buildSizedBoxH(30.0),
                            _buildNextButton(),
                          ],
                        ] else ...[
                          buildSizedBoxH(30.0),
                          _buildGstTitleAndDescription(context, state),
                          buildSizedBoxH(30.0),
                          _buildGSTNumberField(context, state),
                          if (state.isGSTNumberVerify) ...[
                            buildSizedBoxH(24.0),
                            _buildVerifyGSTNameField(context, state),
                          ],
                          buildSizedBoxH(24.0),
                          _buildUploadGSTCertificate(state, context),
                          buildSizedBoxH(30.0),
                          _buildNextButton(),
                        ],
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnnualTurnOverOptions(BuildContext context, BusinessAccountSetupState state) {
    return FutureBuilder<List<String>>(
      future: _getCachedTurnoverList(context),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError || !snapshot.hasData) {
          return const SizedBox.shrink();
        }
        final turnoverList = snapshot.data!;
        return Column(
          children: List.generate(turnoverList.length, (index) {
            return CustomTileWidget(
              title: turnoverList[index],
              isSelected: state.selectedAnnualTurnover == turnoverList[index],
              onTap: () {
                final bloc = BlocProvider.of<BusinessAccountSetupBloc>(context);
                bloc.add(ChangeAnnualTurnover(turnoverList[index]));
              },
            );
          }),
        );
      },
    );
  }

  Widget _buildSelectionTitleAndDescription({
    required BuildContext context,
    required String title,
    required String description,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 28, tablet: 30, desktop: 32),
            fontWeight: FontWeight.w500,
            letterSpacing: 0.32,
          ),
        ),
        buildSizedBoxH(14.0),
        Text(
          description,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            color: Theme.of(context).customColors.secondaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildGstTitleAndDescription(BuildContext context, BusinessAccountSetupState state) {
    return state.isGstCertificateMandatory
        ? _buildSelectionTitleAndDescription(
          context: context,
          title: Lang.of(context).lbl_gst_detail_required,
          description: Lang.of(context).lbl_gst_detail_required_content,
        )
        : _buildSelectionTitleAndDescription(
          context: context,
          title: Lang.of(context).lbl_gst_detail_optional,
          description: Lang.of(context).lbl_gst_detail_optional_content,
        );
  }

  Widget _buildGSTNumberField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_gst_number,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          controller: state.gstNumberController,
          maxLength: 15,
          textInputAction: TextInputAction.done,
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
          validator: ExchekValidations.validateGST,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          onFieldSubmitted: (value) {
            if (state.annualTurnoverFormKey.currentState!.validate()) {
              context.read<BusinessAccountSetupBloc>().add(
                BusinessGSTVerification(
                  gstNumber: state.gstNumberController.text,
                  turnover: state.selectedAnnualTurnover.toString(),
                ),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildUploadGSTCertificate(BusinessAccountSetupState state, BuildContext context) {
    return CustomFileUploadWidget(
      selectedFile: state.gstCertificateFile,
      title: Lang.of(context).lbl_upload_GST_Certificate,
      onFileSelected: (fileData) {
        context.read<BusinessAccountSetupBloc>().add(UploadGstCertificateFile(fileData: fileData));
      },
    );
  }

  Widget _buildNextButton() {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return AnimatedBuilder(
          animation: Listenable.merge([state.turnOverController, state.gstNumberController]),
          builder: (context, _) {
            final isButtonEnabled = state.isGstCertificateMandatory == true ? state.gstCertificateFile != null : true;

            return Align(
              alignment: Alignment.centerRight,
              child: CustomElevatedButton(
                isShowTooltip: true,
                text: Lang.of(context).save_and_next,
                borderRadius: 8.0,
                width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 120 : double.maxFinite,
                buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 16.0,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
                tooltipMessage: Lang.of(context).lbl_tooltip_text,
                isDisabled: !isButtonEnabled,
                isLoading: state.isGstVerificationLoading ?? false,
                onPressed:
                    isButtonEnabled
                        ? () {
                          if (state.isGstCertificateMandatory == true) {
                            if (state.annualTurnoverFormKey.currentState!.validate()) {
                              context.read<BusinessAccountSetupBloc>().add(
                                AnnualTurnOverVerificationSubmitted(
                                  gstCertificate: state.gstCertificateFile,
                                  gstNumber: state.gstNumberController.text,
                                ),
                              );
                            }
                          } else {
                            if (state.gstNumberController.text.isNotEmpty && state.gstCertificateFile != null) {
                              if (state.annualTurnoverFormKey.currentState!.validate()) {
                                context.read<BusinessAccountSetupBloc>().add(
                                  AnnualTurnOverVerificationSubmitted(
                                    gstCertificate: state.gstCertificateFile,
                                    gstNumber: state.gstNumberController.text,
                                  ),
                                );
                              }
                            } else {
                              final currentIndex = state.currentKycVerificationStep.index;
                              if (currentIndex < PersonalEKycVerificationSteps.values.length - 1) {
                                context.read<BusinessAccountSetupBloc>().add(
                                  KycStepChanged(KycVerificationSteps.values[currentIndex + 1]),
                                );
                              }
                            }
                          }
                        }
                        : null,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildVerifyGSTNameField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Legal Entity Name",
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CommanVerifiedInfoBox(value: state.gstLegalName ?? ''),
      ],
    );
  }

  Widget _buildVerifyButton() {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return AnimatedBuilder(
          animation: Listenable.merge([state.gstNumberController]),
          builder: (context, _) {
            final isButtonEnabled = state.gstNumberController.text.isNotEmpty && state.selectedAnnualTurnover != null;
            return Align(
              alignment: Alignment.centerRight,
              child: CustomElevatedButton(
                isShowTooltip: true,
                text: Lang.of(context).lbl_verify,
                borderRadius: 8.0,
                width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 125 : double.maxFinite,
                buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 16.0,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
                tooltipMessage: Lang.of(context).lbl_tooltip_text,
                isDisabled: !isButtonEnabled,
                isLoading: state.isGstVerificationLoading ?? false,
                onPressed:
                    isButtonEnabled
                        ? () {
                          if (state.annualTurnoverFormKey.currentState!.validate()) {
                            context.read<BusinessAccountSetupBloc>().add(
                              BusinessGSTVerification(
                                gstNumber: state.gstNumberController.text,
                                turnover: state.selectedAnnualTurnover.toString(),
                              ),
                            );
                          }
                        }
                        : null,
              ),
            );
          },
        );
      },
    );
  }
}
