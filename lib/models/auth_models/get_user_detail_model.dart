class GetUserDetailModel {
  bool? success;
  Data? data;

  GetUserDetailModel({this.success, this.data});

  GetUserDetailModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? userId;
  String? userEmail;
  String? userType;
  String? mobileNumber;
  List<String>? multicurrency;
  String? estimatedMonthlyVolume;
  BusinessDetails? businessDetails;
  PersonalDetails? personalDetails;
  List<UserIdentityDocuments>? userIdentityDocuments;
  UserAddressDocuments? userAddressDocuments;
  UserGstDetails? userGstDetails;
  List<UserBusinessLegalDocuments>? userBusinessLegalDocuments;

  Data({
    this.userId,
    this.userEmail,
    this.userType,
    this.mobileNumber,
    this.multicurrency,
    this.estimatedMonthlyVolume,
    this.businessDetails,
    this.personalDetails,
    this.userIdentityDocuments,
    this.userAddressDocuments,
    this.userGstDetails,
    this.userBusinessLegalDocuments,
  });

  Data.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    userEmail = json['user_email'];
    userType = json['user_type'];
    mobileNumber = json['mobile_number'];
    multicurrency =
        (json['multicurrency'] is List)
            ? List<String>.from((json['multicurrency'] as List).map((e) => e?.toString() ?? ''))
            : null;
    estimatedMonthlyVolume = json['estimated_monthly_volume'];
    businessDetails = json['business_details'] != null ? BusinessDetails.fromJson(json['business_details']) : null;
    personalDetails = json['personal_details'] != null ? PersonalDetails.fromJson(json['personal_details']) : null;
    if (json['user_identity_documents'] != null) {
      userIdentityDocuments = <UserIdentityDocuments>[];
      json['user_identity_documents'].forEach((v) {
        userIdentityDocuments!.add(UserIdentityDocuments.fromJson(v));
      });
    }
    userAddressDocuments =
        json['user_address_documents'] != null ? UserAddressDocuments.fromJson(json['user_address_documents']) : null;
    userGstDetails = json['user_gst_details'] != null ? UserGstDetails.fromJson(json['user_gst_details']) : null;
    if (json['user_business_legal_documents'] != null) {
      userBusinessLegalDocuments = <UserBusinessLegalDocuments>[];
      json['user_business_legal_documents'].forEach((v) {
        userBusinessLegalDocuments!.add(UserBusinessLegalDocuments.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = userId;
    data['user_email'] = userEmail;
    data['user_type'] = userType;
    data['mobile_number'] = mobileNumber;
    data['multicurrency'] = multicurrency;
    data['estimated_monthly_volume'] = estimatedMonthlyVolume;
    if (businessDetails != null) {
      data['business_details'] = businessDetails!.toJson();
    }
    if (personalDetails != null) {
      data['personal_details'] = personalDetails!.toJson();
    }
    if (userIdentityDocuments != null) {
      data['user_identity_documents'] = userIdentityDocuments!.map((v) => v.toJson()).toList();
    }
    if (userAddressDocuments != null) {
      data['user_address_documents'] = userAddressDocuments!.toJson();
    }
    if (userGstDetails != null) {
      data['user_gst_details'] = userGstDetails!.toJson();
    }
    if (userBusinessLegalDocuments != null) {
      data['user_business_legal_documents'] = userBusinessLegalDocuments!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class BusinessDetails {
  String? businessType;
  String? businessNature;
  List<String>? exportsType;
  String? businessLegalName;

  BusinessDetails({this.businessType, this.businessNature, this.exportsType, this.businessLegalName});

  BusinessDetails.fromJson(Map<String, dynamic> json) {
    businessType = json['business_type'];
    businessNature = json['business_nature'];
    exportsType =
        (json['exports_type'] is List)
            ? List<String>.from((json['exports_type'] as List).map((e) => e?.toString() ?? ''))
            : null;
    businessLegalName = json['business_legal_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['business_type'] = businessType;
    data['business_nature'] = businessNature;
    data['exports_type'] = exportsType;
    data['business_legal_name'] = businessLegalName;
    return data;
  }
}

class PersonalDetails {
  String? legalFullName;
  String? paymentPurpose;
  String? productDesc;
  List<String>? profession;

  PersonalDetails({this.legalFullName, this.paymentPurpose, this.productDesc, this.profession});

  PersonalDetails.fromJson(Map<String, dynamic> json) {
    legalFullName = json['legal_full_name'];
    paymentPurpose = json['payment_purpose'];
    productDesc = json['product_desc'];
    profession =
        (json['profession'] is List)
            ? List<String>.from((json['profession'] as List).map((e) => e?.toString() ?? ''))
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['legal_full_name'] = legalFullName;
    data['payment_purpose'] = paymentPurpose;
    data['product_desc'] = productDesc;
    data['profession'] = profession;
    return data;
  }
}

class UserIdentityDocuments {
  String? documentType;
  String? documentNumber;
  String? frontDocUrl;
  String? backDocUrl;
  String? kycRole;
  String? nameOnPan;

  UserIdentityDocuments({
    this.documentType,
    this.documentNumber,
    this.frontDocUrl,
    this.backDocUrl,
    this.kycRole,
    this.nameOnPan,
  });

  UserIdentityDocuments.fromJson(Map<String, dynamic> json) {
    documentType = json['document_type'];
    documentNumber = json['document_number'];
    frontDocUrl = json['front_doc_url'];
    backDocUrl = json['back_doc_url'];
    kycRole = json['kyc_role'];
    nameOnPan = json['name_on_pan'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['document_type'] = documentType;
    data['document_number'] = documentNumber;
    data['front_doc_url'] = frontDocUrl;
    data['back_doc_url'] = backDocUrl;
    data['kyc_role'] = kycRole;
    data['name_on_pan'] = nameOnPan;

    return data;
  }
}

class UserAddressDocuments {
  String? documentType;
  String? country;
  String? pincode;
  String? state;
  String? city;
  String? addressLine1;
  String? frontDocUrl;

  UserAddressDocuments({
    this.documentType,
    this.country,
    this.pincode,
    this.state,
    this.city,
    this.addressLine1,
    this.frontDocUrl,
  });

  UserAddressDocuments.fromJson(Map<String, dynamic> json) {
    documentType = json['document_type'];
    country = json['country'];
    pincode = json['pincode'];
    state = json['state'];
    city = json['city'];
    addressLine1 = json['address_line1'];
    frontDocUrl = json['front_doc_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['document_type'] = documentType;
    data['country'] = country;
    data['pincode'] = pincode;
    data['state'] = state;
    data['city'] = city;
    data['address_line1'] = addressLine1;
    data['front_doc_url'] = frontDocUrl;
    return data;
  }
}

class UserGstDetails {
  String? gstNumber;
  String? legalName;
  String? gstCertificateUrl;
  String? estimatedAnnualIncome;

  UserGstDetails({this.gstNumber, this.legalName, this.gstCertificateUrl, this.estimatedAnnualIncome});

  UserGstDetails.fromJson(Map<String, dynamic> json) {
    gstNumber = json['gst_number'];
    legalName = json['legal_name'];
    gstCertificateUrl = json['gst_certificate_url'];
    estimatedAnnualIncome = json['estimated_annual_income'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['gst_number'] = gstNumber;
    data['legal_name'] = legalName;
    data['gst_certificate_url'] = gstCertificateUrl;
    data['estimated_annual_income'] = estimatedAnnualIncome;
    return data;
  }
}

class UserBusinessLegalDocuments {
  String? documentType;
  String? documentNumber;
  String? docUrl;

  UserBusinessLegalDocuments({this.documentType, this.documentNumber, this.docUrl});

  UserBusinessLegalDocuments.fromJson(Map<String, dynamic> json) {
    documentType = json['document_type'];
    documentNumber = json['document_number'];
    docUrl = json['doc_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['document_type'] = documentType;
    data['document_number'] = documentNumber;
    data['doc_url'] = docUrl;
    return data;
  }
}
