class ValidateLoginOtpModel {
  Data? data;
  bool? success;

  ValidateLoginOtpModel({this.data, this.success});

  ValidateLoginOtpModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    success = json['success'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['success'] = success;
    return data;
  }
}

class Data {
  String? token;
  User? user;

  Data({this.token, this.user});

  Data.fromJson(Map<String, dynamic> json) {
    token = json['token'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['token'] = token;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    return data;
  }
}

class User {
  String? createdAt;
  String? email;
  String? mobileNumber;
  String? updatedAt;
  String? userId;
  String? userName;

  User({this.createdAt, this.email, this.mobileNumber, this.updatedAt, this.userId, this.userName});

  User.fromJson(Map<String, dynamic> json) {
    createdAt = json['created_at'];
    email = json['email'];
    mobileNumber = json['mobile_number'];
    updatedAt = json['updated_at'];
    userId = json['user_id'];
    userName = json['user_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['email'] = email;
    data['mobile_number'] = mobileNumber;
    data['updated_at'] = updatedAt;
    data['user_id'] = userId;
    data['user_name'] = userName;
    return data;
  }
}
