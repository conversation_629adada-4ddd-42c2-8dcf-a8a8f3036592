class GetCurrencyOptionModel {
  bool? success;
  Data? data;

  GetCurrencyOptionModel({this.success, this.data});

  GetCurrencyOptionModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  int? id;
  List<String>? estimatedMonthlyVolume;
  List<String>? multicurrency;
  String? createdAt;
  String? updatedAt;

  Data({this.id, this.estimatedMonthlyVolume, this.multicurrency, this.createdAt, this.updatedAt});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    estimatedMonthlyVolume = json['estimated_monthly_volume'].cast<String>();
    multicurrency = json['multicurrency'].cast<String>();
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['estimated_monthly_volume'] = estimatedMonthlyVolume;
    data['multicurrency'] = multicurrency;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
