part of 'personal_account_setup_bloc.dart';

class PersonalAccountSetupState extends Equatable {
  final PersonalAccountSetupSteps currentStep;
  final String? selectedPurpose;
  final List<String>? selectedProfession;
  final ScrollController scrollController;
  final TextEditingController professionOtherController;
  final TextEditingController productServiceDescriptionController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;
  final BuildContext? context;
  final GlobalKey<FormState> personalInfoKey;
  final String? fullName;
  final String? email;
  final String? website;
  final String? phoneNumber;
  final String? password;
  final String? selectedEstimatedMonthlyTransaction;
  final List<CurrencyModel>? currencyList;
  final List<CurrencyModel>? selectedCurrencies;
  final List<String>? estimatedMonthlyVolumeList;
  final bool? isTransactionDetailLoading;
  final bool isPersonalAccount;
  final PersonalEKycVerificationSteps currentKycVerificationStep;
  final IDVerificationDocType? selectedIDVerificationDocType;
  final TextEditingController aadharNumberController;
  final TextEditingController aadharOtpController;
  final bool isOtpSent;
  final int aadharOtpRemainingTime;
  final bool isAadharOtpTimerRunning;
  final String? aadharNumber;
  final bool? isIdVerifiedLoading;
  final bool? isIdVerified;
  final GlobalKey<FormState> aadharVerificationFormKey;
  final GlobalKey<FormState> drivingVerificationFormKey;
  final TextEditingController drivingLicenceController;
  final String? drivingLicenseNumber;
  final bool? isDrivingIdVerifiedLoading;
  final bool? isDrivingIdVerified;
  final FileData? drivingLicenceFrontSideFile;
  final FileData? frontSideAdharFile;
  final FileData? backSideAdharFile;
  final bool isIdFileSubmittedLoading;
  final GlobalKey<FormState> voterVerificationFormKey;
  final TextEditingController voterIdNumberController;
  final String? voterIDNumber;
  final bool? isvoterIdVerifiedLoading;
  final bool? isvoterIdVerified;
  final FileData? voterIdFileData;
  final GlobalKey<FormState> passportVerificationFormKey;
  final TextEditingController passportNumberController;
  final String? passporteNumber;
  final bool? ispassportIdVerifiedLoading;
  final bool? ispassportIdVerified;
  final FileData? passportFileData;
  final GlobalKey<FormState> panVerificationKey;
  final TextEditingController panNameController;
  final TextEditingController panNumberController;
  final FileData? panFileData;
  final bool? isPanVerifyingLoading;
  final GlobalKey<FormState> registerAddressFormKey;
  final Country? selectedCountry;
  final TextEditingController pinCodeController;
  final TextEditingController stateNameController;
  final TextEditingController cityNameController;
  final TextEditingController address1NameController;
  final TextEditingController address2NameController;
  final String? selectedAddressVerificationDocType;
  final FileData? addressVerificationFile;
  final FileData? backAddressVerificationFile;
  final bool? isAddressVerificationLoading;
  final GlobalKey<FormState> annualTurnoverFormKey;
  final TextEditingController turnOverController;
  final TextEditingController gstNumberController;
  final FileData? gstCertificateFile;
  final bool? isGstCertificateMandatory;
  final bool? isGstVerificationLoading;
  final GlobalKey<FormState> personalBankAccountVerificationFormKey;
  final TextEditingController bankAccountNumberController;
  final TextEditingController reEnterbankAccountNumberController;
  final TextEditingController ifscCodeController;
  final String? bankAccountNumber;
  final String? ifscCode;
  final String? accountHolderName;
  final String? selectedBankAccountVerificationDocType;
  final FileData? bankVerificationFile;
  final bool? isBankAccountVerify;
  final bool? isBankAccountNumberVerifiedLoading;
  final bool isLoading;
  final bool isReady;
  final bool hasPermission;
  final CameraController? cameraController;
  final Uint8List? imageBytes;
  final String? errorMessage;
  final bool isImageCaptured;
  final bool isImageSubmitted;
  final bool navigateNext;
  final Timer? scrollDebounceTimer;
  final TextEditingController websiteController;
  final TextEditingController mobileController;
  final TextEditingController otpController;
  final bool isOTPSent;
  final bool isOtpVerified;
  final bool canResendOTP;
  final int timeLeft;
  final String? otpError;
  final bool obscurePassword;
  final bool obscureConfirmPassword;
  final String? error;
  final GlobalKey<FormState> sePasswordFormKey;
  final bool? isSignupSuccess;
  final TextEditingController captchaInputController;
  final String captchaInput;
  final bool isCaptchaValid;
  final bool isCaptchaSubmitting;
  final String? isCaptchError;
  final bool isCaptchaSend;
  final bool? isCaptchaLoading;
  final String? captchaImage;
  final bool isOtpLoading;
  final int isResidenceAddressSameAsAadhar;
  final bool isPanDetailsLoading;
  final String? fullNamePan;
  final bool isPanDetailsVerified;
  final bool isCityAndStateLoading;
  final bool isCityAndStateVerified;
  final bool isAgreeToAddressSameAsAadhar;
  final bool isVerifyPersonalRegisterdInfo;
  final String? isAadharOTPInvalidate;
  final bool showPanNameOverwrittenPopup;
  final TextEditingController fullNameController;
  final TextEditingController familyAndFriendsDescriptionController;
  final String? selectedAnnualTurnover;
  final bool isCollapsed;
  final bool isEkycCollapsed;
  final String? gstLegalName;
  final bool isGSTNumberVerify;
  final GlobalKey<FormState> iceVerificationKey;
  final TextEditingController iceNumberController;
  final FileData? iceCertificateFile;
  final bool? isIceVerifyingLoading;
  final bool isLocalDataLoading;
  final bool? isShowServiceDescriptionBox;

  const PersonalAccountSetupState({
    this.currentStep = PersonalAccountSetupSteps.personalEntity,
    this.selectedPurpose,
    this.selectedProfession,
    required this.scrollController,
    required this.professionOtherController,
    required this.productServiceDescriptionController,
    required this.passwordController,
    required this.confirmPasswordController,
    this.context,
    required this.personalInfoKey,
    this.fullName,
    this.email,
    this.website,
    this.phoneNumber,
    this.password,
    this.selectedEstimatedMonthlyTransaction,
    this.currencyList,
    this.selectedCurrencies,
    this.estimatedMonthlyVolumeList,
    this.isTransactionDetailLoading,
    this.isPersonalAccount = true,
    this.selectedIDVerificationDocType,
    this.isOtpSent = false,
    this.aadharOtpRemainingTime = 0,
    this.isAadharOtpTimerRunning = false,
    required this.currentKycVerificationStep,
    required this.aadharNumberController,
    required this.aadharOtpController,
    this.aadharNumber,
    this.isIdVerifiedLoading = false,
    this.isIdVerified = false,
    this.isDrivingIdVerifiedLoading,
    this.isDrivingIdVerified,
    required this.aadharVerificationFormKey,
    required this.drivingVerificationFormKey,
    required this.drivingLicenceController,
    this.drivingLicenseNumber,
    this.drivingLicenceFrontSideFile,
    this.frontSideAdharFile,
    this.backSideAdharFile,
    this.isIdFileSubmittedLoading = false,
    required this.voterVerificationFormKey,
    required this.voterIdNumberController,
    this.voterIDNumber,
    this.isvoterIdVerifiedLoading,
    this.isvoterIdVerified,
    this.voterIdFileData,
    required this.passportVerificationFormKey,
    required this.passportNumberController,
    this.passporteNumber,
    this.ispassportIdVerifiedLoading,
    this.ispassportIdVerified,
    this.passportFileData,
    required this.panVerificationKey,
    required this.panNameController,
    required this.panNumberController,
    this.panFileData,
    this.isPanVerifyingLoading,
    required this.registerAddressFormKey,
    this.selectedCountry,
    required this.pinCodeController,
    required this.stateNameController,
    required this.cityNameController,
    required this.address1NameController,
    required this.address2NameController,
    this.selectedAddressVerificationDocType,
    this.addressVerificationFile,
    this.backAddressVerificationFile,
    this.isAddressVerificationLoading,
    required this.annualTurnoverFormKey,
    required this.turnOverController,
    required this.gstNumberController,
    this.gstCertificateFile,
    this.isGstCertificateMandatory,
    this.isGstVerificationLoading,
    required this.personalBankAccountVerificationFormKey,
    required this.bankAccountNumberController,
    required this.reEnterbankAccountNumberController,
    required this.ifscCodeController,
    this.bankAccountNumber,
    this.ifscCode,
    this.accountHolderName,
    this.selectedBankAccountVerificationDocType,
    this.bankVerificationFile,
    this.isBankAccountVerify,
    this.isBankAccountNumberVerifiedLoading,
    this.isLoading = false,
    this.isReady = false,
    this.hasPermission = false,
    this.cameraController,
    this.imageBytes,
    this.errorMessage,
    this.isImageCaptured = false,
    this.isImageSubmitted = false,
    this.navigateNext = false,
    this.scrollDebounceTimer,
    required this.websiteController,
    required this.mobileController,
    required this.otpController,
    this.isOTPSent = false,
    this.isOtpVerified = false,
    this.canResendOTP = false,
    this.timeLeft = 30,
    this.otpError,
    this.obscurePassword = true,
    this.obscureConfirmPassword = true,
    this.error,
    required this.sePasswordFormKey,
    this.isSignupSuccess,
    required this.captchaInputController,
    this.captchaInput = '',
    this.isCaptchaValid = false,
    this.isCaptchaSubmitting = false,
    this.isCaptchError,
    this.isCaptchaSend = false,
    this.isCaptchaLoading = false,
    this.captchaImage,
    this.isOtpLoading = false,
    this.isResidenceAddressSameAsAadhar = 1,
    this.isPanDetailsLoading = false,
    this.fullNamePan,
    this.isPanDetailsVerified = false,
    this.isCityAndStateLoading = false,
    this.isCityAndStateVerified = false,
    this.isAgreeToAddressSameAsAadhar = false,
    this.isVerifyPersonalRegisterdInfo = false,
    this.isAadharOTPInvalidate,
    this.showPanNameOverwrittenPopup = false,
    required this.fullNameController,
    required this.familyAndFriendsDescriptionController,
    this.selectedAnnualTurnover,
    this.isCollapsed = false,
    this.isEkycCollapsed = false,
    this.gstLegalName,
    this.isGSTNumberVerify = false,
    required this.iceVerificationKey,
    required this.iceNumberController,
    this.iceCertificateFile,
    this.isIceVerifyingLoading = false,
    this.isLocalDataLoading = false,
    this.isShowServiceDescriptionBox = false,
  });

  @override
  List<Object?> get props => [
    currentStep,
    selectedPurpose,
    selectedProfession,
    scrollController,
    professionOtherController,
    productServiceDescriptionController,
    passwordController.text,
    confirmPasswordController.text,
    personalInfoKey,
    fullName,
    website,
    phoneNumber,
    password,
    selectedEstimatedMonthlyTransaction,
    currencyList,
    selectedCurrencies,
    estimatedMonthlyVolumeList,
    isTransactionDetailLoading,
    isPersonalAccount,
    isOtpSent,
    selectedIDVerificationDocType,
    aadharOtpRemainingTime,
    isAadharOtpTimerRunning,
    currentKycVerificationStep,
    aadharNumberController,
    aadharOtpController,
    aadharNumber,
    isIdVerifiedLoading,
    isIdVerified,
    aadharVerificationFormKey,
    drivingVerificationFormKey,
    drivingLicenceController,
    drivingLicenseNumber,
    isDrivingIdVerifiedLoading,
    isDrivingIdVerified,
    frontSideAdharFile,
    drivingLicenceFrontSideFile,
    backSideAdharFile,
    isIdFileSubmittedLoading,
    voterVerificationFormKey,
    voterIdNumberController,
    voterIDNumber,
    isvoterIdVerifiedLoading,
    isvoterIdVerified,
    voterIdFileData,
    passportVerificationFormKey,
    passportNumberController,
    passporteNumber,
    ispassportIdVerifiedLoading,
    ispassportIdVerified,
    passportFileData,
    panVerificationKey,
    panNameController,
    panNumberController,
    panFileData,
    isPanVerifyingLoading,
    registerAddressFormKey,
    selectedCountry,
    pinCodeController,
    stateNameController,
    cityNameController,
    address1NameController,
    address2NameController,
    selectedAddressVerificationDocType,
    addressVerificationFile,
    backAddressVerificationFile,
    isAddressVerificationLoading,
    annualTurnoverFormKey,
    turnOverController,
    gstNumberController,
    gstCertificateFile,
    isGstCertificateMandatory,
    isGstVerificationLoading,
    personalBankAccountVerificationFormKey,
    bankAccountNumberController,
    reEnterbankAccountNumberController,
    ifscCodeController,
    bankAccountNumber,
    ifscCode,
    accountHolderName,
    selectedBankAccountVerificationDocType,
    bankVerificationFile,
    isBankAccountVerify,
    isBankAccountNumberVerifiedLoading,
    isLoading,
    isReady,
    hasPermission,
    cameraController,
    imageBytes,
    errorMessage,
    isImageCaptured,
    isImageSubmitted,
    navigateNext,
    scrollDebounceTimer,
    websiteController,
    mobileController,
    otpController,
    isOTPSent,
    isOtpVerified,
    canResendOTP,
    timeLeft,
    otpError,
    obscurePassword,
    obscureConfirmPassword,
    error,
    sePasswordFormKey,
    isSignupSuccess,
    captchaInputController,
    captchaInput,
    isCaptchaValid,
    isCaptchaSubmitting,
    isCaptchError,
    isCaptchaSend,
    isDrivingIdVerified,
    isDrivingIdVerifiedLoading,
    isCaptchaLoading,
    captchaImage,
    isOtpLoading,
    isResidenceAddressSameAsAadhar,
    isPanDetailsLoading,
    fullNamePan,
    isPanDetailsVerified,
    isCityAndStateLoading,
    isCityAndStateVerified,
    isAgreeToAddressSameAsAadhar,
    isVerifyPersonalRegisterdInfo,
    fullNameController,
    familyAndFriendsDescriptionController,
    selectedAnnualTurnover,
    isCollapsed,
    isEkycCollapsed,
    showPanNameOverwrittenPopup,
    gstLegalName,
    isGSTNumberVerify,
    iceVerificationKey,
    iceNumberController,
    iceCertificateFile,
    isIceVerifyingLoading,
    isLocalDataLoading,
    isShowServiceDescriptionBox,
  ];

  PersonalAccountSetupState copyWith({
    PersonalAccountSetupSteps? currentStep,
    String? selectedPurpose,
    List<String>? selectedProfession,
    ScrollController? scrollController,
    TextEditingController? professionOtherController,
    TextEditingController? productServiceDescriptionController,
    TextEditingController? passwordController,
    TextEditingController? confirmPasswordController,
    BuildContext? context,
    GlobalKey<FormState>? personalInfoKey,
    String? fullName,
    String? email,
    String? website,
    String? phoneNumber,
    String? password,
    String? selectedEstimatedMonthlyTransaction,
    List<CurrencyModel>? currencyList,
    List<CurrencyModel>? selectedCurrencies,
    List<String>? estimatedMonthlyVolumeList,
    bool? isTransactionDetailLoading,
    bool? isPersonalAccount,
    bool? isOtpSent,
    IDVerificationDocType? selectedIDVerificationDocType,
    int? aadharOtpRemainingTime,
    bool? isAadharOtpTimerRunning,
    PersonalEKycVerificationSteps? currentKycVerificationStep,
    TextEditingController? aadharNumberController,
    TextEditingController? aadharOtpController,
    String? aadharNumber,
    bool? isIdVerifiedLoading,
    bool? isIdVerified,
    bool? isDrivingIdVerifiedLoading,
    bool? isDrivingIdVerified,
    GlobalKey<FormState>? aadharVerificationFormKey,
    GlobalKey<FormState>? drivingVerificationFormKey,
    TextEditingController? drivingLicenceController,
    String? drivingLicenseNumber,
    FileData? drivingLicenceFrontSideFile,
    FileData? frontSideAdharFile,
    FileData? backSideAdharFile,
    bool? isIdFileSubmittedLoading,
    GlobalKey<FormState>? voterVerificationFormKey,
    TextEditingController? voterIdNumberController,
    String? voterIDNumber,
    bool? isvoterIdVerifiedLoading,
    bool? isvoterIdVerified,
    FileData? voterIdFileData,
    GlobalKey<FormState>? passportVerificationFormKey,
    TextEditingController? passportNumberController,
    String? passporteNumber,
    bool? ispassportIdVerifiedLoading,
    bool? ispassportIdVerified,
    FileData? passportFileData,
    GlobalKey<FormState>? panVerificationKey,
    TextEditingController? panNameController,
    TextEditingController? panNumberController,
    FileData? panFileData,
    bool? isPanVerifyingLoading,
    GlobalKey<FormState>? registerAddressFormKey,
    Country? selectedCountry,
    TextEditingController? pinCodeController,
    TextEditingController? stateNameController,
    TextEditingController? cityNameController,
    TextEditingController? address1NameController,
    TextEditingController? address2NameController,
    String? selectedAddressVerificationDocType,
    FileData? addressVerificationFile,
    FileData? backAddressVerificationFile,
    bool? isAddressVerificationLoading,
    GlobalKey<FormState>? annualTurnoverFormKey,
    TextEditingController? turnOverController,
    TextEditingController? gstNumberController,
    FileData? gstCertificateFile,
    bool? isGstCertificateMandatory,
    bool? isGstVerificationLoading,
    GlobalKey<FormState>? personalBankAccountVerificationFormKey,
    TextEditingController? bankAccountNumberController,
    TextEditingController? reEnterbankAccountNumberController,
    TextEditingController? ifscCodeController,
    String? bankAccountNumber,
    String? ifscCode,
    String? accountHolderName,
    String? selectedBankAccountVerificationDocType,
    FileData? bankVerificationFile,
    bool? isBankAccountVerify,
    bool? isBankAccountNumberVerifiedLoading,
    bool? isLoading,
    bool? isReady,
    bool? hasPermission,
    CameraController? cameraController,
    Uint8List? imageBytes,
    String? errorMessage,
    bool? isImageCaptured,
    bool? isImageSubmitted,
    bool? navigateNext,
    Timer? scrollDebounceTimer,
    TextEditingController? websiteController,
    TextEditingController? mobileController,
    TextEditingController? otpController,
    bool? isOTPSent,
    bool? isOtpVerified,
    bool? canResendOTP,
    int? timeLeft,
    String? otpError,
    bool? obscurePassword,
    bool? obscureConfirmPassword,
    String? error,
    GlobalKey<FormState>? sePasswordFormKey,
    bool? isSignupSuccess,
    bool? isSignupLoading,
    bool? isSignupError,
    TextEditingController? captchaInputController,
    String? captchaInput,
    bool? isCaptchaValid,
    bool? isCaptchaSubmitting,
    String? isCaptchError,
    bool? isCaptchaSend,
    bool? isCaptchaLoading,
    String? captchaImage,
    bool? isOtpLoading,
    int? isResidenceAddressSameAsAadhar,
    bool? isPanDetailsLoading,
    String? fullNamePan,
    bool? isPanDetailsVerified,
    bool? isCityAndStateLoading,
    bool? isCityAndStateVerified,
    bool? isAgreeToAddressSameAsAadhar,
    bool? isVerifyPersonalRegisterdInfo,
    String? isAadharOTPInvalidate,
    bool? showPanNameOverwrittenPopup,
    TextEditingController? fullNameController,
    TextEditingController? familyAndFriendsDescriptionController,
    String? selectedAnnualTurnover,
    bool? isCollapsed,
    bool? isEkycCollapsed,
    String? gstLegalName,
    bool? isGSTNumberVerify,
    GlobalKey<FormState>? iceVerificationKey,
    TextEditingController? iceNumberController,
    FileData? iceCertificateFile,
    bool? isIceVerifyingLoading,
    bool? isLocalDataLoading,
    bool? isShowServiceDescriptionBox,
  }) {
    return PersonalAccountSetupState(
      currentStep: currentStep ?? this.currentStep,
      selectedPurpose: selectedPurpose ?? this.selectedPurpose,
      selectedProfession: selectedProfession ?? this.selectedProfession,
      scrollController: scrollController ?? this.scrollController,
      professionOtherController: professionOtherController ?? this.professionOtherController,
      productServiceDescriptionController:
          productServiceDescriptionController ?? this.productServiceDescriptionController,
      passwordController: passwordController ?? this.passwordController,
      confirmPasswordController: confirmPasswordController ?? this.confirmPasswordController,
      context: context ?? this.context,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      website: website ?? this.website,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      password: password ?? this.password,
      selectedEstimatedMonthlyTransaction:
          selectedEstimatedMonthlyTransaction ?? this.selectedEstimatedMonthlyTransaction,
      currencyList: currencyList ?? this.currencyList,
      selectedCurrencies: selectedCurrencies ?? this.selectedCurrencies,
      estimatedMonthlyVolumeList: estimatedMonthlyVolumeList ?? this.estimatedMonthlyVolumeList,
      isTransactionDetailLoading: isTransactionDetailLoading ?? this.isTransactionDetailLoading,
      isPersonalAccount: isPersonalAccount ?? this.isPersonalAccount,
      isOtpSent: isOtpSent ?? this.isOtpSent,
      selectedIDVerificationDocType: selectedIDVerificationDocType ?? this.selectedIDVerificationDocType,
      aadharOtpRemainingTime: aadharOtpRemainingTime ?? this.aadharOtpRemainingTime,
      isAadharOtpTimerRunning: isAadharOtpTimerRunning ?? this.isAadharOtpTimerRunning,
      currentKycVerificationStep: currentKycVerificationStep ?? this.currentKycVerificationStep,
      aadharNumberController: aadharNumberController ?? this.aadharNumberController,
      aadharOtpController: aadharOtpController ?? this.aadharOtpController,
      aadharNumber: aadharNumber ?? this.aadharNumber,
      isIdVerifiedLoading: isIdVerifiedLoading ?? this.isIdVerifiedLoading,
      isIdVerified: isIdVerified ?? this.isIdVerified,
      aadharVerificationFormKey: aadharVerificationFormKey ?? this.aadharVerificationFormKey,
      drivingVerificationFormKey: drivingVerificationFormKey ?? this.drivingVerificationFormKey,
      drivingLicenceController: drivingLicenceController ?? this.drivingLicenceController,
      drivingLicenseNumber: drivingLicenseNumber ?? this.drivingLicenseNumber,
      drivingLicenceFrontSideFile: drivingLicenceFrontSideFile ?? this.drivingLicenceFrontSideFile,
      frontSideAdharFile: frontSideAdharFile ?? this.frontSideAdharFile,
      backSideAdharFile: backSideAdharFile ?? this.backSideAdharFile,
      isIdFileSubmittedLoading: isIdFileSubmittedLoading ?? this.isIdFileSubmittedLoading,
      voterVerificationFormKey: voterVerificationFormKey ?? this.voterVerificationFormKey,
      voterIdNumberController: voterIdNumberController ?? this.voterIdNumberController,
      voterIDNumber: voterIDNumber ?? this.voterIDNumber,
      isvoterIdVerifiedLoading: isvoterIdVerifiedLoading ?? this.isvoterIdVerifiedLoading,
      isvoterIdVerified: isvoterIdVerified ?? this.isvoterIdVerified,
      voterIdFileData: voterIdFileData ?? this.voterIdFileData,
      passportVerificationFormKey: passportVerificationFormKey ?? this.passportVerificationFormKey,
      passportNumberController: passportNumberController ?? this.passportNumberController,
      passporteNumber: passporteNumber ?? this.passporteNumber,
      ispassportIdVerifiedLoading: ispassportIdVerifiedLoading ?? this.ispassportIdVerifiedLoading,
      ispassportIdVerified: ispassportIdVerified ?? this.ispassportIdVerified,
      passportFileData: passportFileData ?? this.passportFileData,
      panVerificationKey: panVerificationKey ?? this.panVerificationKey,
      panNameController: panNameController ?? this.panNameController,
      panNumberController: panNumberController ?? this.panNumberController,
      panFileData: panFileData ?? this.panFileData,
      isPanVerifyingLoading: isPanVerifyingLoading ?? this.isPanVerifyingLoading,
      registerAddressFormKey: registerAddressFormKey ?? this.registerAddressFormKey,
      selectedCountry: selectedCountry ?? this.selectedCountry,
      pinCodeController: pinCodeController ?? this.pinCodeController,
      stateNameController: stateNameController ?? this.stateNameController,
      cityNameController: cityNameController ?? this.cityNameController,
      address1NameController: address1NameController ?? this.address1NameController,
      address2NameController: address2NameController ?? this.address2NameController,
      selectedAddressVerificationDocType: selectedAddressVerificationDocType ?? this.selectedAddressVerificationDocType,
      addressVerificationFile: addressVerificationFile ?? this.addressVerificationFile,
      backAddressVerificationFile: backAddressVerificationFile ?? this.backAddressVerificationFile,
      isAddressVerificationLoading: isAddressVerificationLoading ?? this.isAddressVerificationLoading,
      annualTurnoverFormKey: annualTurnoverFormKey ?? this.annualTurnoverFormKey,
      turnOverController: turnOverController ?? this.turnOverController,
      gstNumberController: gstNumberController ?? this.gstNumberController,
      gstCertificateFile: gstCertificateFile ?? this.gstCertificateFile,
      isGstCertificateMandatory: isGstCertificateMandatory ?? this.isGstCertificateMandatory,
      isGstVerificationLoading: isGstVerificationLoading ?? this.isGstVerificationLoading,
      personalBankAccountVerificationFormKey:
          personalBankAccountVerificationFormKey ?? this.personalBankAccountVerificationFormKey,
      bankAccountNumberController: bankAccountNumberController ?? this.bankAccountNumberController,
      reEnterbankAccountNumberController: reEnterbankAccountNumberController ?? this.reEnterbankAccountNumberController,
      ifscCodeController: ifscCodeController ?? this.ifscCodeController,
      bankAccountNumber: bankAccountNumber ?? this.bankAccountNumber,
      bankVerificationFile: bankVerificationFile ?? this.bankVerificationFile,
      ifscCode: ifscCode ?? this.ifscCode,
      accountHolderName: accountHolderName ?? this.accountHolderName,
      selectedBankAccountVerificationDocType:
          selectedBankAccountVerificationDocType ?? this.selectedBankAccountVerificationDocType,
      isBankAccountVerify: isBankAccountVerify ?? this.isBankAccountVerify,
      isBankAccountNumberVerifiedLoading: isBankAccountNumberVerifiedLoading ?? this.isBankAccountNumberVerifiedLoading,
      isLoading: isLoading ?? this.isLoading,
      isReady: isReady ?? this.isReady,
      hasPermission: hasPermission ?? this.hasPermission,
      cameraController: cameraController ?? this.cameraController,
      imageBytes: imageBytes,
      errorMessage: errorMessage,
      isImageCaptured: isImageCaptured ?? this.isImageCaptured,
      isImageSubmitted: isImageSubmitted ?? this.isImageSubmitted,
      navigateNext: navigateNext ?? this.navigateNext,
      scrollDebounceTimer: scrollDebounceTimer ?? this.scrollDebounceTimer,
      websiteController: websiteController ?? this.websiteController,
      mobileController: mobileController ?? this.mobileController,
      otpController: otpController ?? this.otpController,
      isOTPSent: isOTPSent ?? this.isOTPSent,
      isOtpVerified: isOtpVerified ?? this.isOtpVerified,
      canResendOTP: canResendOTP ?? this.canResendOTP,
      timeLeft: timeLeft ?? this.timeLeft,
      otpError: otpError ?? this.otpError,
      obscurePassword: obscurePassword ?? this.obscurePassword,
      obscureConfirmPassword: obscureConfirmPassword ?? this.obscureConfirmPassword,
      error: error ?? this.error,
      personalInfoKey: personalInfoKey ?? this.personalInfoKey,
      sePasswordFormKey: sePasswordFormKey ?? this.sePasswordFormKey,
      isSignupSuccess: isSignupSuccess ?? this.isSignupSuccess,
      captchaInputController: captchaInputController ?? this.captchaInputController,
      captchaInput: captchaInput ?? this.captchaInput,
      isCaptchaValid: isCaptchaValid ?? this.isCaptchaValid,
      isCaptchaSubmitting: isCaptchaSubmitting ?? this.isCaptchaSubmitting,
      isCaptchError: isCaptchError ?? this.isCaptchError,
      isCaptchaSend: isCaptchaSend ?? this.isCaptchaSend,
      isDrivingIdVerified: isDrivingIdVerified ?? this.isDrivingIdVerified,
      isDrivingIdVerifiedLoading: isDrivingIdVerifiedLoading ?? this.isDrivingIdVerifiedLoading,
      isCaptchaLoading: isCaptchaLoading ?? this.isCaptchaLoading,
      captchaImage: captchaImage ?? this.captchaImage,
      isOtpLoading: isOtpLoading ?? this.isOtpLoading,
      isResidenceAddressSameAsAadhar: isResidenceAddressSameAsAadhar ?? this.isResidenceAddressSameAsAadhar,
      isPanDetailsLoading: isPanDetailsLoading ?? this.isPanDetailsLoading,
      fullNamePan: fullNamePan ?? this.fullNamePan,
      isPanDetailsVerified: isPanDetailsVerified ?? this.isPanDetailsVerified,
      isCityAndStateLoading: isCityAndStateLoading ?? this.isCityAndStateLoading,
      isCityAndStateVerified: isCityAndStateVerified ?? this.isCityAndStateVerified,
      isAgreeToAddressSameAsAadhar: isAgreeToAddressSameAsAadhar ?? this.isAgreeToAddressSameAsAadhar,
      isVerifyPersonalRegisterdInfo: isVerifyPersonalRegisterdInfo ?? this.isVerifyPersonalRegisterdInfo,
      isAadharOTPInvalidate: isAadharOTPInvalidate ?? this.isAadharOTPInvalidate,
      fullNameController: fullNameController ?? this.fullNameController,
      familyAndFriendsDescriptionController:
          familyAndFriendsDescriptionController ?? this.familyAndFriendsDescriptionController,
      selectedAnnualTurnover: selectedAnnualTurnover ?? this.selectedAnnualTurnover,
      isCollapsed: isCollapsed ?? this.isCollapsed,
      isEkycCollapsed: isEkycCollapsed ?? this.isEkycCollapsed,
      showPanNameOverwrittenPopup: showPanNameOverwrittenPopup ?? this.showPanNameOverwrittenPopup,
      gstLegalName: gstLegalName ?? this.gstLegalName,
      isGSTNumberVerify: isGSTNumberVerify ?? this.isGSTNumberVerify,
      iceVerificationKey: iceVerificationKey ?? this.iceVerificationKey,
      iceNumberController: iceNumberController ?? this.iceNumberController,
      iceCertificateFile: iceCertificateFile ?? this.iceCertificateFile,
      isIceVerifyingLoading: isIceVerifyingLoading ?? this.isIceVerifyingLoading,
      isLocalDataLoading: isLocalDataLoading ?? this.isLocalDataLoading,
      isShowServiceDescriptionBox: isShowServiceDescriptionBox ?? this.isShowServiceDescriptionBox,
    );
  }
}
