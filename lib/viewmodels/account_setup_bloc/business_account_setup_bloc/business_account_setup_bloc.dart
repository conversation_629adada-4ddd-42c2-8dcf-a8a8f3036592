// ignore_for_file: invalid_use_of_visible_for_testing_member

import 'dart:convert';

import 'package:country_picker/country_picker.dart';
import 'package:cron/cron.dart';
import 'package:dio/dio.dart';
import 'package:exchek/core/utils/exports.dart';
import 'package:equatable/equatable.dart';
import 'package:exchek/models/personal_user_models/aadhar_otp_model.dart';
import 'package:exchek/models/personal_user_models/aadhar_verify_otp_model.dart';
import 'package:exchek/models/personal_user_models/captcha_model.dart';
import 'package:exchek/models/personal_user_models/get_city_and_state_model.dart';
import 'package:exchek/models/personal_user_models/get_pan_detail_model.dart';
import 'package:exchek/models/personal_user_models/recaptcha_model.dart';
import 'package:exchek/repository/business_user_kyc_repository.dart';
import 'package:exchek/repository/personal_user_kyc_repository.dart';
import 'package:exchek/views/account_setup_view/business_account_setup_view/business_transaction_and_payment_preferences_view.dart';
import 'package:exchek/widgets/common_widget/app_toast_message.dart';
import 'package:exchek/models/personal_user_models/get_currency_model.dart';

part 'business_account_setup_event.dart';
part 'business_account_setup_state.dart';

class BusinessAccountSetupBloc extends Bloc<BusinessAccountSetupEvent, BusinessAccountSetupState> {
  static const int initialTime = 120;
  Timer? _timer;

  Timer? _aadhartimer;

  Timer? _kartaAadhartimer;
  final AuthRepository _authRepository;
  final BusinessUserKycRepository _businessUserKycRepository;
  final PersonalUserKycRepository _personalUserKycRepository;
  Cron? _cron;

  // Static GlobalKeys to prevent conflicts
  static final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _sePasswordFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _aadharVerificationFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _kartaAadharVerificationFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _hufPanVerificationKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _businessPanVerificationKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _directorsPanVerificationKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _beneficialOwnerPanVerificationKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _businessRepresentativeFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _registerAddressFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _annualTurnoverFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _iceVerificationKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _cinVerificationKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _bankAccountVerificationFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _directorContactInfoFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _otherDirectorsPanVerificationKey = GlobalKey<FormState>();

  BusinessAccountSetupBloc({
    required AuthRepository authRepository,
    required BusinessUserKycRepository businessUserKycRepository,
    required PersonalUserKycRepository personalUserKycRepository,
  }) : _authRepository = authRepository,
       _businessUserKycRepository = businessUserKycRepository,
       _personalUserKycRepository = personalUserKycRepository,

       super(
         BusinessAccountSetupState(
           currentStep: BusinessAccountSetupSteps.businessEntity,
           goodsAndServiceExportDescriptionController: TextEditingController(),
           goodsExportOtherController: TextEditingController(),
           serviceExportOtherController: TextEditingController(),
           businessActivityOtherController: TextEditingController(),
           scrollController: ScrollController(),
           formKey: _formKey,
           businessLegalNameController: TextEditingController(),
           professionalWebsiteUrl: TextEditingController(),
           phoneController: TextEditingController(),
           otpController: TextEditingController(),
           sePasswordFormKey: _sePasswordFormKey,
           createPasswordController: TextEditingController(),
           confirmPasswordController: TextEditingController(),
           currentKycVerificationStep: KycVerificationSteps.aadharVerfication,
           aadharNumberController: TextEditingController(),
           aadharOtpController: TextEditingController(),
           aadharVerificationFormKey: _aadharVerificationFormKey,
           kartaAadharVerificationFormKey: _kartaAadharVerificationFormKey,
           kartaAadharNumberController: TextEditingController(),
           kartaAadharOtpController: TextEditingController(),
           hufPanVerificationKey: _hufPanVerificationKey,
           hufPanNumberController: TextEditingController(),
           isHUFPanVerifyingLoading: false,
           businessPanNumberController: TextEditingController(),
           businessPanNameController: TextEditingController(),
           businessPanVerificationKey: _businessPanVerificationKey,
           directorsPanVerificationKey: _directorsPanVerificationKey,
           director1PanNumberController: TextEditingController(),
           director1PanNameController: TextEditingController(),
           director2PanNumberController: TextEditingController(),
           director2PanNameController: TextEditingController(),
           beneficialOwnerPanVerificationKey: _beneficialOwnerPanVerificationKey,
           beneficialOwnerPanNumberController: TextEditingController(),
           beneficialOwnerPanNameController: TextEditingController(),
           businessRepresentativeFormKey: _businessRepresentativeFormKey,
           businessRepresentativePanNumberController: TextEditingController(),
           businessRepresentativePanNameController: TextEditingController(),
           registerAddressFormKey: _registerAddressFormKey,
           pinCodeController: TextEditingController(),
           stateNameController: TextEditingController(),
           cityNameController: TextEditingController(),
           address1NameController: TextEditingController(),
           address2NameController: TextEditingController(),
           annualTurnoverFormKey: _annualTurnoverFormKey,
           turnOverController: TextEditingController(),
           iceVerificationKey: _iceVerificationKey,
           iceNumberController: TextEditingController(),
           cinVerificationKey: _cinVerificationKey,
           cinNumberController: TextEditingController(),
           llpinNumberController: TextEditingController(),
           bankAccountVerificationFormKey: _bankAccountVerificationFormKey,
           bankAccountNumberController: TextEditingController(),
           reEnterbankAccountNumberController: TextEditingController(),
           ifscCodeController: TextEditingController(),
           gstNumberController: TextEditingController(),
           isGstCertificateMandatory: false,
           selectedCountry: Country(
             phoneCode: '91',
             countryCode: 'IN',
             e164Sc: 0,
             geographic: true,
             level: 1,
             name: 'India',
             example: '**********',
             displayName: 'India',
             displayNameNoCountryCode: 'India',
             e164Key: '',
           ),
           directorCaptchaInputController: TextEditingController(),
           partnerAadharNumberController: TextEditingController(),
           partnerAadharOtpController: TextEditingController(),
           partnerAadharVerificationFormKey: GlobalKey<FormState>(),
           partnerFrontSideAdharFile: null,
           partnerBackSideAdharFile: null,
           isPartnerOtpSent: false,
           isPartnerOtpLoading: false,
           partnerIsAadharOTPInvalidate: null,
           isPartnerAadharVerifiedLoading: false,
           isPartnerAadharVerified: false,
           partnerAadharNumber: null,
           partnerCaptchaInputController: TextEditingController(),
           kartaCaptchaInputController: TextEditingController(),
           proprietorAadharNumberController: TextEditingController(),
           proprietorAadharOtpController: TextEditingController(),
           proprietorAadharVerificationFormKey: GlobalKey<FormState>(),
           proprietorFrontSideAdharFile: null,
           proprietorBackSideAdharFile: null,
           isProprietorOtpSent: false,
           isProprietorOtpLoading: false,
           proprietorIsAadharOTPInvalidate: null,
           isProprietorAadharVerifiedLoading: false,
           isProprietorAadharVerified: false,
           proprietorAadharNumber: null,
           proprietorCaptchaInputController: TextEditingController(),
           directorEmailIdNumberController: TextEditingController(),
           directorMobileNumberController: TextEditingController(),
           directorContactInformationKey: _directorContactInfoFormKey,
           otherDirectorsPanVerificationKey: _otherDirectorsPanVerificationKey,

           // Other Director Aadhar related properties
           otherDirectorVerificationFormKey: GlobalKey<FormState>(),
           otherDirectorAadharNumberController: TextEditingController(),
           otherDirectoraadharOtpController: TextEditingController(),
           otherDirectorCaptchaInputController: TextEditingController(),
           directorKycStep: DirectorKycSteps.panDetails,
           companyPanNumberController: TextEditingController(),
           companyPanVerificationKey: GlobalKey<FormState>(),
           companyPanCardFile: null,
           isCompanyPanDetailsLoading: false,
           isCompanyPanDetailsVerified: false,
           fullCompanyNamePan: null,
           isCompanyPanVerifyingLoading: false,
         ),
       ) {
    on<StepChanged>(_onStepChange);
    on<ChangeBusinessEntityType>(_onChangeBusinessEnityType);
    on<ChangeBusinessMainActivity>(_onChangeBusinessMainActivity);
    on<ChangeBusinessGoodsExport>(_onChangeBusinessGoodsExport);
    on<ChangeBusinessServicesExport>(_onChangeBusinessServicesExport);
    on<ScrollToSection>(_onScrollToSection);
    on<CancelScrollDebounce>(_onCancelScrollDebounce);
    on<BusinessSendOtpPressed>(_onSendOtpPressed);
    on<BusinessOtpTimerTicked>(_onOtpTimerTicked);
    on<ChangeConfirmPasswordVisibility>(_onChangeConfirmPasswordVisibility);
    on<ChangeCreatePasswordVisibility>(_onChangeCreatePasswordVisibility);
    on<BusinessAccountSignUpSubmitted>(_onBusinessAccountSignUpSubmitted);
    on<ResetSignupSuccess>(_onResetSignupSuccess);
    on<SendBusinessInfoOtp>(_onSendBusinessInfoOtp);
    on<KycStepChanged>(_onKycStepChange);
    on<SendAadharOtp>(_onSendAadharOtp);
    on<ChangeOtpSentStatus>(_onChangeOtpSentStatus);
    on<AadharSendOtpPressed>(_onAadharSendOtpPressed);
    on<AadharOtpTimerTicked>(_onAadharOtpTimerTicked);
    on<AadharNumbeVerified>(_onAadharNumbeVerified);
    on<FrontSlideAadharCardUpload>(_onFrontSlideAadharCardUpload);
    on<BackSlideAadharCardUpload>(_onBackSlideAadharCardUpload);
    on<KartaSendAadharOtp>(_onKartaSendAadharOtp);
    on<KartaChangeOtpSentStatus>(_onKartaChangeOtpSentStatus);
    on<KartaAadharSendOtpPressed>(_onKartaAadharSendOtpPressed);
    on<KartaAadharOtpTimerTicked>(_onKartaAadharOtpTimerTicked);
    on<KartaAadharNumbeVerified>(_onKartaAadharNumbeVerified);
    on<KartaFrontSlideAadharCardUpload>(_onKartaFrontSlideAadharCardUpload);
    on<KartaBackSlideAadharCardUpload>(_onKartaBackSlideAadharCardUpload);
    on<UploadHUFPanCard>(_onUploadHUFPanCard);
    on<HUFPanVerificationSubmitted>(_onHUFPanVerificationSubmitted);
    on<AadharFileUploadSubmitted>(_onAadharFileUploadSubmitted);
    on<KartaAadharFileUploadSubmitted>(_onKartaAadharFileUploadSubmitted);
    on<ChangeSelectedPanUploadOption>(_onChangeSelectedPanUploadOption);
    on<BusinessUploadPanCard>(_onBusinessUploadPanCard);
    on<SaveBusinessPanDetails>(_onSaveBusinessPanDetails);
    on<Director1UploadPanCard>(_onDirector1UploadPanCard);
    on<Director2UploadPanCard>(_onDirector2UploadPanCard);
    on<SaveDirectorPanDetails>(_onSaveDirectorPanDetails);
    on<ChangeDirector1IsBeneficialOwner>(_onChangeDirector1IsBeneficialOwner);
    on<ChangeDirector2IsBeneficialOwner>(_onChangeDirector2IsBeneficialOwner);
    on<ChangeDirector1IsBusinessRepresentative>(_onChangeDirector1IsBusinessRepresentative);
    on<ChangeDirector2IsBusinessRepresentative>(_onChangeDirector2IsBusinessRepresentative);
    on<BeneficialOwnerUploadPanCard>(_onBeneficialOwnerUploadPanCard);
    on<ChangeBeneficialOwnerIsDirector>(_onChangeBeneficialOwnerIsDirector);
    on<ChangeBeneficialOwnerIsBusinessRepresentative>(_onChangeBeneficialOwnerIsBusinessRepresentative);
    on<SaveBeneficialOwnerPanDetails>(_onSaveBeneficialPanDetails);
    on<BusinessRepresentativeUploadPanCard>(_onBusinessRepresentativeUploadPanCard);
    on<ChangeBusinessReresentativeIsBeneficialOwner>(_onChangeBusinessReresentativeIsBeneficialOwner);
    on<ChangeBusinessReresentativeOwnerIsDirector>(_onChangeBusinessReresentativeOwnerIsDirector);
    on<SaveBusinessRepresentativePanDetails>(_onSaveBusinessRepresentativePanDetails);
    on<VerifyPanSubmitted>(_onVerifyPanSubmitted);
    on<UpdateSelectedCountry>(_onUpdateSelectedCountry);
    on<UploadAddressVerificationFile>(_onUploadAddressVerificationFile);
    on<UpdateAddressVerificationDocType>(_onUpdateAddressVerificationDocType);
    on<RegisterAddressSubmitted>(_onRegisterAddressSubmitted);
    on<AnnualTurnOverVerificationSubmitted>(_onAnnualTurnOverVerificationSubmitted);
    on<UploadICECertificate>(_onUploadICECertificate);
    on<IceNumberChanged>(_onIceNumberChanged);
    on<ICEVerificationSubmitted>(_onICEVerificationSubmitted);
    on<UploadGstCertificateFile>(_onUploadGstCertificateFile);
    on<UploadCOICertificate>(_onUploadCOICertificate);
    on<UploadLLPAgreement>(_onUploadLLPAgreement);
    on<UploadPartnershipDeed>(_onUploadPartnershipDeed);
    on<CINVerificationSubmitted>(_onCINVerificationSubmitted);
    on<BankAccountNumberVerify>(_onBankAccountNumberVerify);
    on<UpdateBankAccountVerificationDocType>(_onUpdateBankAccountVerificationDocType);
    on<UploadBankAccountVerificationFile>(_onUploadBankAccountVerificationFile);
    on<BankAccountDetailSubmitted>(_onBankAccountDetailSubmitted);
    on<ChangeEstimatedMonthlyTransaction>(_onChangeEstimatedMonthlyTransaction);
    on<ToggleCurrencySelection>(_onToggleCurrencySelection);
    on<BusinessTranscationDetailSubmitted>(_onBusinessTranscationDetailSubmitted);
    on<ResetData>(_onResetData);
    on<ValidateBusinessOtp>(_onValidateBusinessOtp);
    on<UpdateBusinessNatureString>(_onUpdateBusinessNatureString);
    on<GetBusinessCurrencyOptions>(_onGetBusinessCurrencyOptions);
    on<ChangeAnnualTurnover>(_onChangeAnnualTurnover);
    on<BusinessAppBarCollapseChanged>(_onBusinessAppBarCollapseChanged);
    on<BusinessEkycAppBarCollapseChanged>(_onBusinessEkycAppBarCollapseChanged);
    on<DirectorCaptchaSend>(_onDirectorCaptchaSend);
    on<DirectorReCaptchaSend>(_onDirectorReCaptchaSend);
    on<KartaCaptchaSend>(_onKartaCaptchaSend);
    on<KartaReCaptchaSend>(_onKartaReCaptchaSend);
    on<PartnerSendAadharOtp>(_onPartnerSendAadharOtp);
    on<PartnerChangeOtpSentStatus>(_onPartnerChangeOtpSentStatus);
    on<PartnerAadharSendOtpPressed>(_onPartnerAadharSendOtpPressed);
    on<PartnerAadharOtpTimerTicked>(_onPartnerAadharOtpTimerTicked);
    on<PartnerAadharNumbeVerified>(_onPartnerAadharNumbeVerified);
    on<PartnerFrontSlideAadharCardUpload>(_onPartnerFrontSlideAadharCardUpload);
    on<PartnerBackSlideAadharCardUpload>(_onPartnerBackSlideAadharCardUpload);
    on<PartnerAadharFileUploadSubmitted>(_onPartnerAadharFileUploadSubmitted);
    on<PartnerCaptchaSend>(_onPartnerCaptchaSend);
    on<PartnerReCaptchaSend>(_onPartnerReCaptchaSend);
    on<ProprietorSendAadharOtp>(_onProprietorSendAadharOtp);
    on<ProprietorChangeOtpSentStatus>(_onProprietorChangeOtpSentStatus);
    on<ProprietorAadharSendOtpPressed>(_onProprietorAadharSendOtpPressed);
    on<ProprietorAadharOtpTimerTicked>(_onProprietorAadharOtpTimerTicked);
    on<ProprietorAadharNumbeVerified>(_onProprietorAadharNumbeVerified);
    on<ProprietorFrontSlideAadharCardUpload>(_onProprietorFrontSlideAadharCardUpload);
    on<ProprietorBackSlideAadharCardUpload>(_onProprietorBackSlideAadharCardUpload);
    on<ProprietorAadharFileUploadSubmitted>(_onProprietorAadharFileUploadSubmitted);
    on<ProprietorCaptchaSend>(_onProprietorCaptchaSend);
    on<ProprietorReCaptchaSend>(_onProprietorReCaptchaSend);
    on<DirectorAadharNumberChanged>(_onDirectorAadharNumberChanged);
    on<KartaAadharNumberChanged>(_onKartaAadharNumberChanged);
    on<PartnerAadharNumberChanged>(_onPartnerAadharNumberChanged);
    on<ProprietorAadharNumberChanged>(_onProprietorAadharNumberChanged);
    on<LoadBusinessKycFromLocal>(_onLoadBusinessKycFromLocal);
    on<BusinessGetCityAndState>(_onBusinessGetCityAndState);
    on<LLPINVerificationSubmitted>(_onLLPINVerificationSubmitted);
    on<PartnerShipDeedVerificationSubmitted>(_onPartnerShipDeedVerificationSubmitted);
    on<BusinessGSTVerification>(_onBusinessGSTVerification);
    on<GetHUFPanDetails>(_onGetHUFPanDetails);
    on<HUFPanNumberChanged>(_onHUFPanNumberChanged);
    on<GetDirector1PanDetails>(_onGetDirector1PanDetails);
    on<Director1PanNumberChanged>(_onDirector1PanNumberChanged);
    on<GetDirector2PanDetails>(_onGetDirector2PanDetails);
    on<Director2PanNumberChanged>(_onDirector2PanNumberChanged);
    on<GetBeneficialOwnerPanDetails>(_onGetBeneficialOwnerPanDetails);
    on<BeneficialOwnerPanNumberChanged>(_onBeneficialOwnerPanNumberChanged);
    on<GetBusinessRepresentativePanDetails>(_onGetBusinessRepresentativePanDetails);
    on<BusinessRepresentativePanNumberChanged>(_onBusinessRepresentativePanNumberChanged);
    on<ContactInformationSubmitted>(_onContactInformationSubmitted);
    on<SaveOtherDirectorPanDetails>(_onSaveOtherDirectorPanDetails);

    // Other Director Aadhar Events
    on<OtherDirectorAadharNumberChanged>(_onOtherDirectorAadharNumberChanged);
    on<OtherDirectorCaptchaSend>(_onOtherDirectorCaptchaSend);
    on<OtherDirectorReCaptchaSend>(_onOtherDirectorReCaptchaSend);
    on<OtherDirectorSendAadharOtp>(_onOtherDirectorSendAadharOtp);
    on<OtherDirectorAadharNumbeVerified>(_onOtherDirectorAadharNumbeVerified);
    on<OtherDirectorFrontSlideAadharCardUpload>(_onOtherDirectorFrontSlideAadharCardUpload);
    on<OtherDirectorBackSlideAadharCardUpload>(_onOtherDirectorBackSlideAadharCardUpload);
    on<OtherDirectorAadharFileUploadSubmitted>(_onOtherDirectorAadharFileUploadSubmitted);
    on<OtherDirectorAadharSendOtpPressed>(_onOtherDirectorAadharSendOtpPressed);
    on<OtherDirectorAadharOtpTimerTicked>(_onOtherDirectorAadharOtpTimerTicked);
    on<DirectorKycStepChanged>(_onDirectorKycStepChange);
    on<OtherDirectorKycStepChanged>(_onOtherDirectorKycStepChange);
    on<ShowBusinessRepresentativeSelectionDialog>(_onShowBusinessRepresentativeSelectionDialog);
    on<SelectBusinessRepresentative>(_onSelectBusinessRepresentative);
    on<OtherDirectorShowDialogWidthoutAadharUpload>(_onOtherDirectorShowDialogWidthoutAadharUpload);
    on<ConfirmBusinessRepresentativeAndNextStep>(_onConfirmBusinessRepresentativeAndNextStep);
    on<CompanyPanNumberChanged>(_onCompanyPanNumberChanged);
    on<GetCompanyPanDetails>(_onGetCompanyPanDetails);
    on<UploadCompanyPanCard>(_onUploadCompanyPanCard);
    on<CompanyPanVerificationSubmitted>(_onCompanyPanVerificationSubmitted);
    on<NavigateToNextKycStep>(_onNavigateToNextKycStep);
    on<NavigateToPreviousKycStep>(_onNavigateToPreviousKycStep);
    on<GetAvailableKycSteps>(_onGetAvailableKycSteps);
    // Start cron job every 10 minutes to refresh only file data
    _cron = Cron();
    _cron!.schedule(Schedule.parse('*/10 * * * *'), () async {
      await _refreshKycFileData();
    });
  }

  void _onStepChange(StepChanged event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(currentStep: event.stepIndex));
  }

  void _onChangeBusinessEnityType(ChangeBusinessEntityType event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      await Prefobj.preferences.put(Prefkeys.businessEntityType, event.selectedIndex);
      emit(state.copyWith(selectedBusinessEntityType: event.selectedIndex));
    } catch (e) {
      Logger.error('Error saving business entity type: $e');
    }
  }

  void _onChangeAnnualTurnover(ChangeAnnualTurnover event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      bool isMandatory = !(event.selectedIndex.contains("Less than"));
      emit(
        state.copyWith(
          selectedAnnualTurnover: event.selectedIndex,
          isGstCertificateMandatory: isMandatory,
          isGSTNumberVerify: false,
        ),
      );
    } catch (e) {
      Logger.error('Error saving business entity type: $e');
    }
  }

  void _onChangeEstimatedMonthlyTransaction(
    ChangeEstimatedMonthlyTransaction event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    try {
      emit(state.copyWith(selectedEstimatedMonthlyTransaction: event.selectedTransaction));
    } catch (e) {
      Logger.error('Error saving business entity type: $e');
    }
  }

  void _onChangeBusinessMainActivity(ChangeBusinessMainActivity event, Emitter<BusinessAccountSetupState> emit) {
    state.goodsExportOtherController.clear();
    state.serviceExportOtherController.clear();
    state.businessActivityOtherController.clear();
    state.goodsAndServiceExportDescriptionController.clear();
    emit(
      state.copyWith(
        selectedBusinessMainActivity: event.selected,
        selectedbusinessGoodsExportType: [],
        selectedbusinessServiceExportType: [],
      ),
    );
    // The string value should be set from the UI by dispatching UpdateBusinessNatureString
  }

  void _onChangeBusinessGoodsExport(ChangeBusinessGoodsExport event, Emitter<BusinessAccountSetupState> emit) {
    final currentSelections = List<String>.from(state.selectedbusinessGoodsExportType ?? []);
    if (event.selectedIndex == 'Others') {
      emit(state.copyWith(selectedbusinessGoodsExportType: ['Others']));
    } else {
      if (currentSelections.contains('Others')) {
        currentSelections.remove('Others');
      }
      if (currentSelections.contains(event.selectedIndex)) {
        currentSelections.remove(event.selectedIndex);
      } else {
        currentSelections.add(event.selectedIndex);
      }
      emit(state.copyWith(selectedbusinessGoodsExportType: currentSelections));
    }
  }

  void _onChangeBusinessServicesExport(ChangeBusinessServicesExport event, Emitter<BusinessAccountSetupState> emit) {
    final currentSelections = List<String>.from(state.selectedbusinessServiceExportType ?? []);
    if (event.selectedIndex == 'Others') {
      emit(state.copyWith(selectedbusinessServiceExportType: ['Others']));
    } else {
      if (currentSelections.contains('Others')) {
        currentSelections.remove('Others');
      }
      if (currentSelections.contains(event.selectedIndex)) {
        currentSelections.remove(event.selectedIndex);
      } else {
        currentSelections.add(event.selectedIndex);
      }
      emit(state.copyWith(selectedbusinessServiceExportType: currentSelections));
    }
  }

  Future<void> _onSendOtpPressed(BusinessSendOtpPressed event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isSendOtpLoading: true));
      final mobileAvailability = await _authRepository.mobileAvailability(mobileNumber: state.phoneController.text);
      if (mobileAvailability.data?.exists == true) {
        AppToast.show(message: 'Mobile number already exists', type: ToastificationType.error);
        emit(state.copyWith(isSendOtpLoading: false));
        return;
      }

      final response = await _authRepository.sendOtp(mobile: state.phoneController.text, type: 'registration');
      if (response.success == true) {
        AppToast.show(message: response.message ?? '', type: ToastificationType.success);
        _timer?.cancel();
        emit(
          state.copyWith(
            isOtpTimerRunning: true,
            otpRemainingTime: initialTime,
            isBusinessInfoOtpSent: true,
            isSendOtpLoading: false,
          ),
        );
        _timer = Timer.periodic(Duration(seconds: 1), (timer) {
          final newTime = state.otpRemainingTime - 1;
          if (newTime <= 0) {
            timer.cancel();
            add(BusinessOtpTimerTicked(0));
          } else {
            add(BusinessOtpTimerTicked(newTime));
          }
        });
      }
    } catch (e) {
      emit(state.copyWith(isSendOtpLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onOtpTimerTicked(BusinessOtpTimerTicked event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(otpRemainingTime: event.remainingTime, isOtpTimerRunning: event.remainingTime > 0));
  }

  void _onChangeCreatePasswordVisibility(
    ChangeCreatePasswordVisibility event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(isCreatePasswordObscure: event.obscuredText));
  }

  void _onChangeConfirmPasswordVisibility(
    ChangeConfirmPasswordVisibility event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(isConfirmPasswordObscure: event.obscuredText));
  }

  void _onBusinessAccountSignUpSubmitted(
    BusinessAccountSignUpSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isSignupLoading: true));
    try {
      final emailId = await Prefobj.preferences.get(Prefkeys.verifyemailToken) ?? '';
      final tosacceptance = await UserAgentHelper.getPlatformMetaInfo();
      final List<String> multicurrencyStrings =
          (state.selectedCurrencies ?? [])
              .map((currency) => '${currency.currencySymbol} ${currency.currencyName}')
              .toList();

      List<String> exportstype = [];
      if (state.selectedBusinessMainActivity == BusinessMainActivity.others) {
        if (state.businessActivityOtherController.text.trim().isNotEmpty) {
          exportstype = [state.businessActivityOtherController.text.trim()];
        }
      } else {
        if ((state.selectedbusinessGoodsExportType?.isNotEmpty ?? false)) {
          if (state.selectedbusinessGoodsExportType!.length == 1 &&
              state.selectedbusinessGoodsExportType!.first == 'Others') {
            if (state.goodsExportOtherController.text.trim().isNotEmpty) {
              exportstype = [state.goodsExportOtherController.text.trim()];
            } else {
              exportstype = state.selectedbusinessGoodsExportType!;
            }
          } else {
            exportstype = state.selectedbusinessGoodsExportType!;
          }
        } else if ((state.selectedbusinessServiceExportType?.isNotEmpty ?? false)) {
          if (state.selectedbusinessServiceExportType!.length == 1 &&
              state.selectedbusinessServiceExportType!.first == 'Others') {
            if (state.serviceExportOtherController.text.trim().isNotEmpty) {
              exportstype = [state.serviceExportOtherController.text.trim()];
            } else {
              exportstype = state.selectedbusinessServiceExportType!;
            }
          } else {
            exportstype = state.selectedbusinessServiceExportType!;
          }
        } else if (state.goodsAndServiceExportDescriptionController.text.trim().isNotEmpty) {
          exportstype = [state.goodsAndServiceExportDescriptionController.text.trim()];
        }
      }

      final response = await _authRepository.registerBusinessUser(
        email: emailId,
        estimatedMonthlyVolume: state.selectedEstimatedMonthlyTransaction ?? '',
        multicurrency: multicurrencyStrings,
        mobileNumber: state.phoneController.text,
        businesstype: state.selectedBusinessEntityType ?? '',
        businessnature: state.businessNatureString ?? '',
        exportstype: exportstype,
        businesslegalname: state.businessLegalNameController.text,
        password: state.createPasswordController.text,
        tosacceptance: tosacceptance,
        usertype: 'business',
        username: state.businessLegalNameController.text,
        website: state.professionalWebsiteUrl.text,
      );
      if (response.success == true) {
        // AppToast.show(message: 'Business user created successfully', type: ToastificationType.success);
        await Prefobj.preferences.put(Prefkeys.authToken, 'exchek@123');
        await Prefobj.preferences.delete(Prefkeys.verifyemailToken);
        emit(state.copyWith(isSignupLoading: false, isSignupSuccess: true));
      } else {
        emit(state.copyWith(isSignupLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isSignupLoading: false));
    }
  }

  void _onResetSignupSuccess(ResetSignupSuccess event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(isSignupSuccess: false));
  }

  void _onSendBusinessInfoOtp(SendBusinessInfoOtp event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      add(BusinessSendOtpPressed());
      emit(state.copyWith(isBusinessInfoOtpSent: true));
    } catch (e) {
      emit(state.copyWith(isBusinessInfoOtpSent: false));
    }
  }

  void _onKycStepChange(KycStepChanged event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(currentKycVerificationStep: event.stepIndex));
  }

  void _onSendAadharOtp(SendAadharOtp event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      state.aadharOtpController.clear();
      emit(state.copyWith(isOtpSent: false, isDirectorAadharOtpLoading: true, isAadharOTPInvalidate: ''));
      AadharOTPSendModel response = await _businessUserKycRepository.generateAadharOTP(
        aadhaarNumber: event.aadhar.replaceAll("-", ""),
        captcha: event.captcha,
        sessionId: event.sessionId,
      );
      if (response.code == 200) {
        emit(state.copyWith(isOtpSent: true, isDirectorAadharOtpLoading: false));
        add(AadharSendOtpPressed());
      } else {
        AppToast.show(message: response.message ?? '', type: ToastificationType.error);
        emit(state.copyWith(isDirectorAadharOtpLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isOtpSent: false, isDirectorAadharOtpLoading: false));
    }
  }

  void _onChangeOtpSentStatus(ChangeOtpSentStatus event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isOtpSent: event.isOtpSent));
  }

  void _onAadharSendOtpPressed(AadharSendOtpPressed event, Emitter<BusinessAccountSetupState> emit) {
    _aadhartimer?.cancel();
    emit(state.copyWith(isAadharOtpTimerRunning: true, aadharOtpRemainingTime: initialTime));
    _aadhartimer = Timer.periodic(Duration(seconds: 1), (timer) {
      final newTime = state.aadharOtpRemainingTime - 1;
      if (newTime <= 0) {
        timer.cancel();
        add(AadharOtpTimerTicked(0));
      } else {
        add(AadharOtpTimerTicked(newTime));
      }
    });
  }

  void _onAadharOtpTimerTicked(AadharOtpTimerTicked event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(aadharOtpRemainingTime: event.remainingTime, isAadharOtpTimerRunning: event.remainingTime > 0));
  }

  void _onAadharNumbeVerified(AadharNumbeVerified event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isAadharVerifiedLoading: true, isAadharOTPInvalidate: null));
    try {
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';

      final AadharOTPVerifyModel response = await _businessUserKycRepository.validateAadharOtp(
        faker: false,
        otp: event.otp,
        sessionId: sessionId,
        userId: userId,
      );
      if (response.code == 200) {
        emit(state.copyWith(isAadharVerifiedLoading: false, isAadharVerified: true, aadharNumber: event.aadharNumber));
        _aadhartimer?.cancel();
      } else {
        emit(state.copyWith(isAadharVerifiedLoading: false, isAadharOTPInvalidate: response.message.toString()));
      }
    } catch (e) {
      emit(state.copyWith(isAadharVerifiedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onFrontSlideAadharCardUpload(FrontSlideAadharCardUpload event, Emitter<BusinessAccountSetupState> emit) {
    if (event.fileData == null) {
      emit(state.copyWith(frontSideAdharFile: null));
    } else {
      emit(state.copyWith(frontSideAdharFile: event.fileData));
    }
  }

  void _onBackSlideAadharCardUpload(BackSlideAadharCardUpload event, Emitter<BusinessAccountSetupState> emit) {
    if (event.fileData == null) {
      emit(state.copyWith(backSideAdharFile: null));
    } else {
      emit(state.copyWith(backSideAdharFile: event.fileData));
    }
  }

  void _onKartaSendAadharOtp(KartaSendAadharOtp event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      state.kartaAadharOtpController.clear();
      emit(state.copyWith(isKartaOtpSent: false, isKartaOtpLoading: true, kartaIsAadharOTPInvalidate: ''));
      AadharOTPSendModel response = await _businessUserKycRepository.generateAadharOTP(
        aadhaarNumber: event.aadhar.replaceAll("-", ""),
        captcha: event.captcha,
        sessionId: event.sessionId,
      );
      if (response.code == 200) {
        emit(state.copyWith(isKartaOtpSent: true, isKartaOtpLoading: false));
        add(KartaAadharSendOtpPressed());
      } else {
        AppToast.show(message: response.message ?? '', type: ToastificationType.error);
        emit(state.copyWith(isKartaOtpLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isKartaOtpSent: false));
    }
  }

  void _onKartaChangeOtpSentStatus(KartaChangeOtpSentStatus event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isKartaOtpSent: event.isOtpSent));
  }

  void _onKartaAadharSendOtpPressed(KartaAadharSendOtpPressed event, Emitter<BusinessAccountSetupState> emit) {
    _kartaAadhartimer?.cancel();
    emit(state.copyWith(isKartaAadharOtpTimerRunning: true, kartaAadharOtpRemainingTime: initialTime));
    _kartaAadhartimer = Timer.periodic(Duration(seconds: 1), (timer) {
      final newTime = state.kartaAadharOtpRemainingTime - 1;
      if (newTime <= 0) {
        timer.cancel();
        add(KartaAadharOtpTimerTicked(0));
      } else {
        add(KartaAadharOtpTimerTicked(newTime));
      }
    });
  }

  void _onKartaAadharOtpTimerTicked(KartaAadharOtpTimerTicked event, Emitter<BusinessAccountSetupState> emit) {
    emit(
      state.copyWith(
        kartaAadharOtpRemainingTime: event.remainingTime,
        isKartaAadharOtpTimerRunning: event.remainingTime > 0,
      ),
    );
  }

  void _onKartaAadharNumbeVerified(KartaAadharNumbeVerified event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isKartaAadharVerifiedLoading: true, kartaIsAadharOTPInvalidate: null));
    try {
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';

      final AadharOTPVerifyModel response = await _businessUserKycRepository.validateAadharOtp(
        faker: false,
        otp: event.otp,
        sessionId: sessionId,
        userId: userId,
      );
      if (response.code == 200) {
        emit(
          state.copyWith(
            isKartaAadharVerifiedLoading: false,
            isKartaAadharVerified: true,
            kartaAadharNumber: event.aadharNumber,
          ),
        );
        _kartaAadhartimer?.cancel();
      } else {
        emit(
          state.copyWith(isKartaAadharVerifiedLoading: false, kartaIsAadharOTPInvalidate: response.message.toString()),
        );
      }
    } catch (e) {
      emit(state.copyWith(isKartaAadharVerifiedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onKartaFrontSlideAadharCardUpload(
    KartaFrontSlideAadharCardUpload event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(kartaFrontSideAdharFile: event.fileData));
  }

  void _onKartaBackSlideAadharCardUpload(
    KartaBackSlideAadharCardUpload event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(kartaBackSideAdharFile: event.fileData));
  }

  void _onUploadHUFPanCard(UploadHUFPanCard event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(hufPanCardFile: event.fileData));
  }

  void _onHUFPanVerificationSubmitted(
    HUFPanVerificationSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isHUFPanVerifyingLoading: true));
    try {
      await Future.delayed(Duration(seconds: 2));
      final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
      if (nextStep != null) {
        add(KycStepChanged(nextStep));
      }
      emit(state.copyWith(isHUFPanVerifyingLoading: false));
    } catch (e) {
      emit(state.copyWith(isHUFPanVerifyingLoading: false));
    }
  }

  void _onIceNumberChanged(IceNumberChanged event, Emitter<BusinessAccountSetupState> emit) {
    final uppercased = event.iceNumber.toUpperCase();

    // Update the controller text if changed to avoid loops
    if (state.iceNumberController.text != uppercased) {
      // Preserve the cursor position
      final selection = state.iceNumberController.selection;
      state.iceNumberController.value = TextEditingValue(text: uppercased, selection: selection);
    }

    // Emit new state with updated controller (optional, controller is updated by ref)
    emit(state.copyWith());
  }

  void _onUploadICECertificate(UploadICECertificate event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(iceCertificateFile: event.fileData));
  }

  void _onICEVerificationSubmitted(ICEVerificationSubmitted event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isIceVerifyingLoading: true));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);
    try {
      final response = await _personalUserKycRepository.uploadBusinessLegalDocuments(
        userID: userId ?? '',
        userType: 'business',
        documentType: 'IEC',
        documentNumber: event.iceNumber ?? '',
        documentFrontImage: event.fileData,
      );
      if (response.success == true) {
        final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
        if (nextStep != null) {
          add(KycStepChanged(nextStep));
        }
        emit(state.copyWith(isIceVerifyingLoading: false));
      } else {
        emit(state.copyWith(isIceVerifyingLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isIceVerifyingLoading: false));
    }
  }

  void _onAadharFileUploadSubmitted(AadharFileUploadSubmitted event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isAadharFileUploading: true, isAuthorizedDirectorKycVerify: false));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    try {
      final response = await _businessUserKycRepository.uploadbusinessKyc(
        userID: userId ?? '',
        documentType: 'Aadhaar',
        documentNumber: state.aadharNumber?.replaceAll("-", "") ?? '',
        documentFrontImage: event.frontAadharFileData!,
        documentBackImage: event.backAadharFileData,
        isAddharCard: true,
        nameOnPan: state.businessPanNameController.text,
        userType: 'business',
        kycRole: 'DIRECTOR',
      );
      if (response.success == true) {
        emit(state.copyWith(isAadharFileUploading: false, isAuthorizedDirectorKycVerify: true));
      } else {
        emit(state.copyWith(isAadharFileUploading: false));
      }
    } catch (e) {
      emit(state.copyWith(isAadharFileUploading: false));
      Logger.error(e.toString());
    }
  }

  void _onKartaAadharFileUploadSubmitted(
    KartaAadharFileUploadSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isKartaAadharFileUploading: true));

    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);
    try {
      final response = await _businessUserKycRepository.uploadbusinessKyc(
        userID: userId ?? '',
        documentType: 'Aadhaar',
        documentNumber: state.kartaAadharNumber?.replaceAll("-", "") ?? '',
        documentFrontImage: event.frontAadharFileData!,
        documentBackImage: event.backAadharFileData,
        isAddharCard: true,
        nameOnPan: '',
        userType: 'business',
        kycRole: "KARTA",
      );
      if (response.success == true) {
        final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
        if (nextStep != null) {
          add(KycStepChanged(nextStep));
        }
        emit(state.copyWith(isKartaAadharFileUploading: false));
      } else {
        emit(state.copyWith(isKartaAadharFileUploading: false));
      }
    } catch (e) {
      emit(state.copyWith(isKartaAadharFileUploading: false));
    }
  }

  void _onChangeSelectedPanUploadOption(ChangeSelectedPanUploadOption event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(selectedUploadPanOption: event.panUploadOption));
  }

  void _onBusinessUploadPanCard(BusinessUploadPanCard event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(businessPanCardFile: event.fileData));
  }

  void _onSaveBusinessPanDetails(SaveBusinessPanDetails event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isBusinessPanCardSaveLoading: true, isBusinessPanCardSave: false));
    try {
      await Future.delayed(Duration(seconds: 2));
      emit(state.copyWith(isBusinessPanCardSaveLoading: false, isBusinessPanCardSave: true));
    } catch (e) {
      Logger.error('Error saving business PAN details: $e');
      emit(state.copyWith(isBusinessPanCardSaveLoading: false));
    }
  }

  void _onDirector1UploadPanCard(Director1UploadPanCard event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(director1PanCardFile: event.fileData));
  }

  void _onDirector2UploadPanCard(Director2UploadPanCard event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(director2PanCardFile: event.fileData));
  }

  void _onSaveDirectorPanDetails(SaveDirectorPanDetails event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isDirectorPanCardSaveLoading: true, isDirectorPanCardSave: false));
    try {
      await Future.delayed(Duration(seconds: 2));
      emit(state.copyWith(isDirectorPanCardSaveLoading: false, isDirectorPanCardSave: true));
      add(DirectorKycStepChanged(DirectorKycSteps.aadharDetails));
    } catch (e) {
      Logger.error('Error saving director PAN details: $e');
      emit(state.copyWith(isDirectorPanCardSaveLoading: false));
    }
  }

  void _onChangeDirector1IsBeneficialOwner(
    ChangeDirector1IsBeneficialOwner event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(director1BeneficialOwner: event.isSelected));
  }

  void _onChangeDirector2IsBeneficialOwner(
    ChangeDirector2IsBeneficialOwner event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(director2BeneficialOwner: event.isSelected));
  }

  void _onChangeDirector1IsBusinessRepresentative(
    ChangeDirector1IsBusinessRepresentative event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(ditector1BusinessRepresentative: event.isSelected));
  }

  void _onChangeDirector2IsBusinessRepresentative(
    ChangeDirector2IsBusinessRepresentative event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(ditector2BusinessRepresentative: event.isSelected));
  }

  void _onBeneficialOwnerUploadPanCard(BeneficialOwnerUploadPanCard event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(beneficialOwnerPanCardFile: event.fileData));
  }

  void _onChangeBeneficialOwnerIsDirector(
    ChangeBeneficialOwnerIsDirector event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(beneficialOwnerIsDirector: event.isSelected));
  }

  void _onChangeBeneficialOwnerIsBusinessRepresentative(
    ChangeBeneficialOwnerIsBusinessRepresentative event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(benificialOwnerBusinessRepresentative: event.isSelected));
  }

  void _onSaveBeneficialPanDetails(SaveBeneficialOwnerPanDetails event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isBeneficialOwnerPanCardSaveLoading: true, isBeneficialOwnerPanCardSave: false));
    try {
      await Future.delayed(Duration(seconds: 2));
      emit(state.copyWith(isBeneficialOwnerPanCardSaveLoading: false, isBeneficialOwnerPanCardSave: true));
    } catch (e) {
      Logger.error('Error saving director PAN details: $e');
      emit(state.copyWith(isBeneficialOwnerPanCardSaveLoading: false));
    }
  }

  void _onBusinessRepresentativeUploadPanCard(
    BusinessRepresentativeUploadPanCard event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(businessRepresentativePanCardFile: event.fileData));
  }

  void _onChangeBusinessReresentativeIsBeneficialOwner(
    ChangeBusinessReresentativeIsBeneficialOwner event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(businessRepresentativeIsBenificalOwner: event.isSelected));
  }

  void _onChangeBusinessReresentativeOwnerIsDirector(
    ChangeBusinessReresentativeOwnerIsDirector event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(businessRepresentativeIsDirector: event.isSelected));
  }

  void _onSaveBusinessRepresentativePanDetails(
    SaveBusinessRepresentativePanDetails event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isbusinessRepresentativePanCardSaveLoading: true, isbusinessRepresentativePanCardSave: false));
    try {
      await Future.delayed(Duration(seconds: 2));
      emit(
        state.copyWith(isbusinessRepresentativePanCardSaveLoading: false, isbusinessRepresentativePanCardSave: true),
      );
    } catch (e) {
      Logger.error('Error saving director PAN details: $e');
      emit(state.copyWith(isbusinessRepresentativePanCardSaveLoading: false));
    }
  }

  void _onVerifyPanSubmitted(VerifyPanSubmitted event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isPanDetailVerifyLoading: true));
    try {
      await Future.delayed(Duration(seconds: 2));
      final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
      if (nextStep != null) {
        add(KycStepChanged(nextStep));
      }
      emit(state.copyWith(isPanDetailVerifyLoading: false, isPanDetailVerifySuccess: true));
    } catch (e) {
      emit(state.copyWith(isPanDetailVerifyLoading: false));
    }
  }

  void _onUpdateSelectedCountry(UpdateSelectedCountry event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(selectedCountry: event.country));
  }

  void _onUploadAddressVerificationFile(UploadAddressVerificationFile event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(addressVerificationFile: event.fileData));
  }

  void _onUploadBankAccountVerificationFile(
    UploadBankAccountVerificationFile event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(bankVerificationFile: event.fileData));
  }

  void _onUploadGstCertificateFile(UploadGstCertificateFile event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(gstCertificateFile: event.fileData));
  }

  void _onUpdateAddressVerificationDocType(
    UpdateAddressVerificationDocType event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(selectedAddressVerificationDocType: event.docType));
  }

  void _onUpdateBankAccountVerificationDocType(
    UpdateBankAccountVerificationDocType event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(selectedBankAccountVerificationDocType: event.docType));
  }

  void _onRegisterAddressSubmitted(RegisterAddressSubmitted event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isAddressVerificationLoading: true));

    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    final user = await Prefobj.preferences.get(Prefkeys.userDetail);
    final userDetail = jsonDecode(user!);

    try {
      final response = await _personalUserKycRepository.uploadResidentialAddressDetails(
        userID: userId ?? '',
        userType: userDetail['user_type'],
        documentType: event.docType ?? '',
        addressLine1: state.address1NameController.text.trim(),
        addressLine2: state.address2NameController.text.trim(),
        city: state.cityNameController.text.trim(),
        isAddharCard: false,
        country: state.selectedCountry?.name ?? '',
        pinCode: state.pinCodeController.text.trim(),
        state: state.stateNameController.text.trim(),
        documentFrontImage: event.addressValidateFileData,
        documentBackImage: null,
        aadhaarUsedAsIdentity: 'no',
      );
      if (response.success == true) {
        final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
        if (nextStep != null) {
          add(KycStepChanged(nextStep));
        }
        emit(state.copyWith(isAddressVerificationLoading: false));
      } else {
        AppToast.show(message: response.message ?? '', type: ToastificationType.error);
        emit(state.copyWith(isAddressVerificationLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isAddressVerificationLoading: false));
    }
  }

  void _onAnnualTurnOverVerificationSubmitted(
    AnnualTurnOverVerificationSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isGstVerificationLoading: true));

    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    try {
      final response = await _personalUserKycRepository.uploadGSTDocument(
        userID: userId ?? '',
        gstNumber: event.gstNumber ?? '',
        userType: 'business',
        gstCertificate: event.gstCertificate,
      );
      if (response.success == true) {
        final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
        if (nextStep != null) {
          add(KycStepChanged(nextStep));
        }
        emit(state.copyWith(isGstVerificationLoading: false));
      } else {
        emit(state.copyWith(isGstVerificationLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isGstVerificationLoading: false));
    }
  }

  void _onUploadCOICertificate(UploadCOICertificate event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(coiCertificateFile: event.fileData));
  }

  void _onUploadLLPAgreement(UploadLLPAgreement event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(uploadLLPAgreementFile: event.fileData));
  }

  void _onUploadPartnershipDeed(UploadPartnershipDeed event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(uploadPartnershipDeed: event.fileData));
  }

  void _onCINVerificationSubmitted(CINVerificationSubmitted event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isCINVerifyingLoading: true));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);
    try {
      final response = await _personalUserKycRepository.uploadBusinessLegalDocuments(
        userID: userId ?? '',
        userType: 'business',
        documentType: 'CIN',
        documentNumber: event.cinNumber ?? '',
        documentFrontImage: event.fileData,
      );
      if (response.success == true) {
        final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
        if (nextStep != null) {
          add(KycStepChanged(nextStep));
        }
        emit(state.copyWith(isCINVerifyingLoading: false));
      } else {
        emit(state.copyWith(isCINVerifyingLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isCINVerifyingLoading: false));
    }
  }

  void _onBankAccountNumberVerify(BankAccountNumberVerify event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isBankAccountNumberVerifiedLoading: true));
    try {
      emit(state.copyWith(isBankAccountNumberVerifiedLoading: true));

      final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

      final user = await Prefobj.preferences.get(Prefkeys.userDetail);
      final userDetail = jsonDecode(user!);

      final response = await _personalUserKycRepository.verifyBankAccount(
        userID: userId ?? '',
        userType: userDetail['user_type'],
        accountNumber: event.accountNumber,
        ifscCode: event.ifscCode,
      );
      if (response.success == true) {
        emit(
          state.copyWith(
            isBankAccountNumberVerifiedLoading: false,
            isBankAccountVerify: true,
            bankAccountNumber: event.accountNumber,
            ifscCode: event.ifscCode,
            accountHolderName: response.data?.accountHolderName ?? '', // from API ideally
          ),
        );
      } else {
        emit(state.copyWith(isBankAccountNumberVerifiedLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isBankAccountNumberVerifiedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onBankAccountDetailSubmitted(BankAccountDetailSubmitted event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isBankAccountNumberVerifiedLoading: true));

    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    final user = await Prefobj.preferences.get(Prefkeys.userDetail);
    final userDetail = jsonDecode(user!);

    try {
      final response = await _personalUserKycRepository.uploadBankDocuments(
        userID: userId ?? '',
        userType: userDetail['user_type'] ?? '',
        accountNumber: state.bankAccountNumber ?? '',
        documentType: event.docType ?? '',
        ifscCode: state.ifscCode ?? '',
        proofDocumentImage: event.bankAccountVerifyFile,
      );
      if (response.success == true) {
        final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
        if (nextStep != null) {
          add(KycStepChanged(nextStep));
        }
        emit(state.copyWith(isBankAccountNumberVerifiedLoading: false));
      } else {
        emit(state.copyWith(isBankAccountNumberVerifiedLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isBankAccountNumberVerifiedLoading: false));
    }
  }

  void _onToggleCurrencySelection(ToggleCurrencySelection event, Emitter<BusinessAccountSetupState> emit) async {
    final currentList = List<CurrencyModel>.from(state.selectedCurrencies ?? []);
    final exists = currentList.any((c) => c.currencySymbol == event.currency.currencySymbol);

    if (exists) {
      currentList.removeWhere((c) => c.currencySymbol == event.currency.currencySymbol);
    } else {
      currentList.add(event.currency);
    }

    emit(state.copyWith(selectedCurrencies: currentList));
  }

  void _onBusinessTranscationDetailSubmitted(
    BusinessTranscationDetailSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isTranscationDetailLoading: true));
    try {
      emit(state.copyWith(isTranscationDetailLoading: false));
      final index = state.currentStep.index;
      if (index < BusinessAccountSetupSteps.values.length - 1) {
        add(StepChanged(BusinessAccountSetupSteps.values[index + 1]));
      }
    } catch (e) {
      emit(state.copyWith(isTranscationDetailLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onScrollToSection(ScrollToSection event, Emitter<BusinessAccountSetupState> emit) {
    state.scrollDebounceTimer?.cancel();

    final scrollController = event.scrollController ?? state.scrollController;

    final newTimer = Timer(const Duration(milliseconds: 300), () {
      final RenderObject? renderObject = event.key.currentContext?.findRenderObject();
      if (renderObject != null) {
        scrollController.position.ensureVisible(
          renderObject,
          alignment: -0.12,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });

    emit(state.copyWith(scrollDebounceTimer: newTimer));
  }

  void _onCancelScrollDebounce(CancelScrollDebounce event, Emitter<BusinessAccountSetupState> emit) {
    state.scrollDebounceTimer?.cancel();
    emit(state.copyWith(scrollDebounceTimer: null));
  }

  void _onResetData(ResetData event, Emitter<BusinessAccountSetupState> emit) {
    // Cancel all timers
    _timer?.cancel();
    _aadhartimer?.cancel();
    _kartaAadhartimer?.cancel();

    // Clear all text controllers
    state.goodsAndServiceExportDescriptionController.clear();
    state.goodsExportOtherController.clear();
    state.serviceExportOtherController.clear();
    state.businessActivityOtherController.clear();
    state.businessLegalNameController.clear();
    state.professionalWebsiteUrl.clear();
    state.phoneController.clear();
    state.otpController.clear();
    state.createPasswordController.clear();
    state.confirmPasswordController.clear();
    state.aadharNumberController.clear();
    state.aadharOtpController.clear();
    state.kartaAadharNumberController.clear();
    state.kartaAadharOtpController.clear();
    state.hufPanNumberController.clear();
    state.businessPanNumberController.clear();
    state.businessPanNameController.clear();
    state.director1PanNumberController.clear();
    state.director1PanNameController.clear();
    state.director2PanNumberController.clear();
    state.director2PanNameController.clear();
    state.beneficialOwnerPanNumberController.clear();
    state.beneficialOwnerPanNameController.clear();
    state.businessRepresentativePanNumberController.clear();
    state.businessRepresentativePanNameController.clear();
    state.pinCodeController.clear();
    state.stateNameController.clear();
    state.cityNameController.clear();
    state.address1NameController.clear();
    state.address2NameController.clear();
    state.turnOverController.clear();
    state.gstNumberController.clear();
    state.iceNumberController.clear();
    state.cinNumberController.clear();
    state.llpinNumberController.clear();
    state.bankAccountNumberController.clear();
    state.reEnterbankAccountNumberController.clear();
    state.ifscCodeController.clear();
    state.partnerAadharNumberController.clear();
    state.partnerAadharOtpController.clear();
    state.proprietorAadharNumberController.clear();
    state.proprietorAadharOtpController.clear();
    state.proprietorCaptchaInputController.clear();
    state.directorCaptchaInputController.clear();
    state.proprietorCaptchaInputController.clear();
    state.directorMobileNumberController.clear();
    state.directorEmailIdNumberController.clear();

    emit(
      BusinessAccountSetupState(
        currentStep: BusinessAccountSetupSteps.businessEntity,
        goodsAndServiceExportDescriptionController: state.goodsAndServiceExportDescriptionController,
        goodsExportOtherController: state.goodsExportOtherController,
        serviceExportOtherController: state.serviceExportOtherController,
        businessActivityOtherController: state.businessActivityOtherController,
        scrollController: state.scrollController,
        formKey: _formKey,
        businessLegalNameController: state.businessLegalNameController,
        professionalWebsiteUrl: state.professionalWebsiteUrl,
        phoneController: state.phoneController,
        otpController: state.otpController,
        sePasswordFormKey: _sePasswordFormKey,
        createPasswordController: state.createPasswordController,
        confirmPasswordController: state.confirmPasswordController,
        currentKycVerificationStep: KycVerificationSteps.aadharVerfication,
        aadharNumberController: state.aadharNumberController,
        aadharOtpController: state.aadharOtpController,
        aadharVerificationFormKey: _aadharVerificationFormKey,
        kartaAadharVerificationFormKey: _kartaAadharVerificationFormKey,
        kartaAadharNumberController: state.kartaAadharNumberController,
        kartaAadharOtpController: state.kartaAadharOtpController,
        hufPanVerificationKey: _hufPanVerificationKey,
        hufPanNumberController: state.hufPanNumberController,
        isHUFPanVerifyingLoading: false,
        businessPanNumberController: state.businessPanNumberController,
        businessPanNameController: state.businessPanNameController,
        businessPanVerificationKey: _businessPanVerificationKey,
        directorsPanVerificationKey: _directorsPanVerificationKey,
        director1PanNumberController: state.director1PanNumberController,
        director1PanNameController: state.director1PanNameController,
        director2PanNumberController: state.director2PanNumberController,
        director2PanNameController: state.director2PanNameController,
        beneficialOwnerPanVerificationKey: _beneficialOwnerPanVerificationKey,
        beneficialOwnerPanNumberController: state.beneficialOwnerPanNumberController,
        beneficialOwnerPanNameController: state.beneficialOwnerPanNameController,
        businessRepresentativeFormKey: _businessRepresentativeFormKey,
        businessRepresentativePanNumberController: state.businessRepresentativePanNumberController,
        businessRepresentativePanNameController: state.businessRepresentativePanNameController,
        registerAddressFormKey: _registerAddressFormKey,
        selectedCountry: Country(
          phoneCode: '91',
          countryCode: 'IN',
          e164Sc: 0,
          geographic: true,
          level: 1,
          name: 'India',
          example: '**********',
          displayName: 'India',
          displayNameNoCountryCode: 'India',
          e164Key: '',
        ),
        pinCodeController: state.pinCodeController,
        stateNameController: state.stateNameController,
        cityNameController: state.cityNameController,
        address1NameController: state.address1NameController,
        address2NameController: state.address2NameController,
        turnOverController: state.turnOverController,
        gstNumberController: state.gstNumberController,
        annualTurnoverFormKey: _annualTurnoverFormKey,
        isGstCertificateMandatory: false,
        iceNumberController: state.iceNumberController,
        iceVerificationKey: _iceVerificationKey,
        cinNumberController: state.cinNumberController,
        cinVerificationKey: _cinVerificationKey,
        llpinNumberController: state.llpinNumberController,
        bankAccountVerificationFormKey: _bankAccountVerificationFormKey,
        bankAccountNumberController: state.bankAccountNumberController,
        reEnterbankAccountNumberController: state.reEnterbankAccountNumberController,
        ifscCodeController: state.ifscCodeController,
        curruncyList: [],
        directorCaptchaInputController: state.directorCaptchaInputController,
        kartaCaptchaInputController: state.kartaCaptchaInputController,
        partnerAadharNumberController: state.partnerAadharNumberController,
        partnerAadharOtpController: state.partnerAadharOtpController,
        partnerAadharVerificationFormKey: state.partnerAadharVerificationFormKey,
        proprietorAadharNumberController: state.proprietorAadharNumberController,
        proprietorAadharOtpController: state.proprietorAadharOtpController,
        proprietorAadharVerificationFormKey: state.proprietorAadharVerificationFormKey,
        partnerCaptchaInputController: state.directorCaptchaInputController,
        proprietorCaptchaInputController: state.proprietorCaptchaInputController,
        directorEmailIdNumberController: state.directorEmailIdNumberController,
        directorMobileNumberController: state.directorMobileNumberController,
        directorContactInformationKey: state.partnerAadharVerificationFormKey,
        otherDirectorsPanVerificationKey: state.otherDirectorsPanVerificationKey,

        // Other Director Aadhar related properties
        otherDirectorVerificationFormKey: state.otherDirectorVerificationFormKey,
        otherDirectorAadharNumberController: state.otherDirectorAadharNumberController,
        otherDirectoraadharOtpController: state.otherDirectoraadharOtpController,
        otherDirectorCaptchaInputController: state.otherDirectorCaptchaInputController,
        directorKycStep: DirectorKycSteps.panDetails,
        companyPanNumberController: state.companyPanNumberController,
        companyPanVerificationKey: state.companyPanVerificationKey,
        companyPanCardFile: state.companyPanCardFile,
        isCompanyPanDetailsLoading: state.isCompanyPanDetailsLoading,
        isCompanyPanDetailsVerified: state.isCompanyPanDetailsVerified,
        fullCompanyNamePan: state.fullCompanyNamePan,
        isCompanyPanVerifyingLoading: state.isCompanyPanVerifyingLoading,
      ),
    );
  }

  void _onValidateBusinessOtp(ValidateBusinessOtp event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isBusinessOtpValidating: true));
    try {
      final response = await _authRepository.validateregistrationOtp(mobile: event.phoneNumber, otp: event.otp);
      if (response.success == true) {
        _timer?.cancel();
        emit(state.copyWith(isBusinessOtpValidating: false, isVerifyBusinessRegisterdInfo: true));
        final index = state.currentStep.index;
        add(StepChanged(BusinessAccountSetupSteps.values[index + 1]));
        state.otpController.clear();
        add(GetBusinessCurrencyOptions());
      }
    } catch (e) {
      emit(state.copyWith(isBusinessOtpValidating: false));
      Logger.error(e.toString());
    }
  }

  void _onUpdateBusinessNatureString(UpdateBusinessNatureString event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(businessNatureString: event.businessNatureString));
  }

  Future<void> _onGetBusinessCurrencyOptions(
    GetBusinessCurrencyOptions event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isSignupLoading: true));
    try {
      final GetCurrencyOptionModel response = await _authRepository.getCurrencyOptions();
      if (response.success == true) {
        final List<CurrencyModel> currencyList =
            (response.data?.multicurrency ?? []).map((currency) {
              final parts = currency.split(' ');
              final symbol = parts[0];
              final name = parts.sublist(1).join(' ');

              return CurrencyModel(
                currencyName: name,
                currencySymbol: symbol,
                currencyImagePath:
                    symbol == "TRY" ? "assets/images/svgs/country/TRI.svg" : "assets/images/svgs/country/$symbol.svg",
              );
            }).toList();
        emit(
          state.copyWith(
            curruncyList: currencyList,
            estimatedMonthlyVolumeList: response.data?.estimatedMonthlyVolume ?? [],
            isSignupLoading: false,
          ),
        );
      } else {
        emit(state.copyWith(isSignupLoading: false));
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(state.copyWith(isSignupLoading: false));
    }
  }

  void _onBusinessAppBarCollapseChanged(BusinessAppBarCollapseChanged event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(isCollapsed: event.isCollapsed));
  }

  void _onBusinessEkycAppBarCollapseChanged(
    BusinessEkycAppBarCollapseChanged event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(isekycCollapsed: event.isCollapsed));
  }

  Future<void> _onDirectorCaptchaSend(DirectorCaptchaSend event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isDirectorCaptchaLoading: true, isDirectorCaptchaSend: false));
      final CaptchaModel response = await _businessUserKycRepository.generateCaptcha();
      if (response.code == 200) {
        emit(
          state.copyWith(
            isDirectorCaptchaSend: true,
            isDirectorCaptchaLoading: false,
            directorCaptchaImage: response.data?.captcha ?? '',
          ),
        );
        await Prefobj.preferences.put(Prefkeys.sessionId, response.data?.sessionId ?? '');
      } else {
        emit(state.copyWith(isDirectorCaptchaLoading: false, isDirectorCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isDirectorCaptchaLoading: false, isDirectorCaptchaSend: false));
      Logger.error('Error :: $e');
    }
  }

  void _onDirectorReCaptchaSend(DirectorReCaptchaSend event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isDirectorCaptchaLoading: true, isDirectorCaptchaSend: false));
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';

      final RecaptchaModel response = await _businessUserKycRepository.reGenerateCaptcha(sessionId: sessionId);
      if (response.code == 200) {
        emit(
          state.copyWith(
            isDirectorCaptchaSend: true,
            isDirectorCaptchaLoading: false,
            directorCaptchaImage: response.data?.captcha ?? '',
          ),
        );
        state.directorCaptchaInputController.clear();
      } else {
        emit(state.copyWith(isDirectorCaptchaLoading: false, isDirectorCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isDirectorCaptchaLoading: false, isDirectorCaptchaSend: false));
      Logger.error(e.toString());
    }
  }

  void _onKartaCaptchaSend(KartaCaptchaSend event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isKartaCaptchaLoading: true, isKartaCaptchaSend: false));
      final CaptchaModel response = await _businessUserKycRepository.generateCaptcha();
      if (response.code == 200) {
        emit(
          state.copyWith(
            isKartaCaptchaSend: true,
            isKartaCaptchaLoading: false,
            kartaCaptchaImage: response.data?.captcha ?? '',
          ),
        );
        await Prefobj.preferences.put(Prefkeys.sessionId, response.data?.sessionId ?? '');
      } else {
        emit(state.copyWith(isKartaCaptchaLoading: false, isKartaCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isKartaCaptchaLoading: false, isKartaCaptchaSend: false));
      Logger.error('Error :: $e');
    }
  }

  void _onKartaReCaptchaSend(KartaReCaptchaSend event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isKartaCaptchaLoading: true, isKartaCaptchaSend: false));
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';

      final RecaptchaModel response = await _businessUserKycRepository.reGenerateCaptcha(sessionId: sessionId);
      if (response.code == 200) {
        emit(
          state.copyWith(
            isKartaCaptchaSend: true,
            isKartaCaptchaLoading: false,
            kartaCaptchaImage: response.data?.captcha ?? '',
          ),
        );
        state.kartaCaptchaInputController.clear();
      } else {
        emit(state.copyWith(isKartaCaptchaLoading: false, isKartaCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isKartaCaptchaLoading: false, isKartaCaptchaSend: false));
      Logger.error(e.toString());
    }
  }

  // ================= PARTNER AADHAAR HANDLERS =================
  void _onPartnerSendAadharOtp(PartnerSendAadharOtp event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      state.partnerAadharOtpController.clear();
      emit(state.copyWith(isPartnerOtpSent: false, isPartnerOtpLoading: true, partnerIsAadharOTPInvalidate: ''));
      AadharOTPSendModel response = await _businessUserKycRepository.generateAadharOTP(
        aadhaarNumber: event.aadhar.replaceAll("-", ""),
        captcha: event.captcha,
        sessionId: event.sessionId,
      );
      if (response.code == 200) {
        emit(state.copyWith(isPartnerOtpSent: true, isPartnerOtpLoading: false));
        add(PartnerAadharSendOtpPressed());
      } else {
        AppToast.show(message: response.message ?? '', type: ToastificationType.error);
        emit(state.copyWith(isPartnerOtpLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isPartnerOtpSent: false));
    }
  }

  void _onPartnerChangeOtpSentStatus(PartnerChangeOtpSentStatus event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isPartnerOtpSent: event.isOtpSent));
  }

  void _onPartnerAadharSendOtpPressed(PartnerAadharSendOtpPressed event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(isPartnerAadharOtpTimerRunning: true, partnerAadharOtpRemainingTime: initialTime));
    Timer.periodic(Duration(seconds: 1), (timer) {
      final newTime = state.partnerAadharOtpRemainingTime - 1;
      if (newTime <= 0) {
        timer.cancel();
        add(PartnerAadharOtpTimerTicked(0));
      } else {
        add(PartnerAadharOtpTimerTicked(newTime));
      }
    });
  }

  void _onPartnerAadharOtpTimerTicked(PartnerAadharOtpTimerTicked event, Emitter<BusinessAccountSetupState> emit) {
    emit(
      state.copyWith(
        partnerAadharOtpRemainingTime: event.remainingTime,
        isPartnerAadharOtpTimerRunning: event.remainingTime > 0,
      ),
    );
  }

  void _onPartnerAadharNumbeVerified(PartnerAadharNumbeVerified event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isPartnerAadharVerifiedLoading: true, partnerIsAadharOTPInvalidate: null));
    try {
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';
      final AadharOTPVerifyModel response = await _businessUserKycRepository.validateAadharOtp(
        faker: false,
        otp: event.otp,
        sessionId: sessionId,
        userId: userId,
      );
      if (response.code == 200) {
        emit(
          state.copyWith(
            isPartnerAadharVerifiedLoading: false,
            isPartnerAadharVerified: true,
            partnerAadharNumber: event.aadharNumber,
          ),
        );
      } else {
        emit(
          state.copyWith(
            isPartnerAadharVerifiedLoading: false,
            partnerIsAadharOTPInvalidate: response.message.toString(),
          ),
        );
      }
    } catch (e) {
      emit(state.copyWith(isPartnerAadharVerifiedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onPartnerFrontSlideAadharCardUpload(
    PartnerFrontSlideAadharCardUpload event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(partnerFrontSideAdharFile: event.fileData));
  }

  void _onPartnerBackSlideAadharCardUpload(
    PartnerBackSlideAadharCardUpload event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(partnerBackSideAdharFile: event.fileData));
  }

  void _onPartnerAadharFileUploadSubmitted(
    PartnerAadharFileUploadSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isPartnerAadharFileUploading: true));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);
    try {
      final response = await _businessUserKycRepository.uploadbusinessKyc(
        userID: userId ?? '',
        documentType: 'Aadhaar',
        documentNumber: state.partnerAadharNumberController.text.replaceAll("-", ""),
        documentFrontImage: event.frontAadharFileData!,
        documentBackImage: event.backAadharFileData,
        isAddharCard: true,
        nameOnPan: '',
        userType: 'business',
        kycRole: "PARTNER",
      );
      if (response.success == true) {
        final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
        if (nextStep != null) {
          add(KycStepChanged(nextStep));
        }
        emit(state.copyWith(isPartnerAadharFileUploading: false));
      } else {
        emit(state.copyWith(isPartnerAadharFileUploading: false));
      }
    } catch (e) {
      emit(state.copyWith(isPartnerAadharFileUploading: false));
      Logger.error(e.toString());
    }
  }

  void _onPartnerCaptchaSend(PartnerCaptchaSend event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isPartnerCaptchaLoading: true, isPartnerCaptchaSend: false));
      final CaptchaModel response = await _businessUserKycRepository.generateCaptcha();
      if (response.code == 200) {
        emit(
          state.copyWith(
            isPartnerCaptchaSend: true,
            isPartnerCaptchaLoading: false,
            partnerCaptchaImage: response.data?.captcha ?? '',
          ),
        );
        await Prefobj.preferences.put(Prefkeys.sessionId, response.data?.sessionId ?? '');
      } else {
        emit(state.copyWith(isPartnerCaptchaLoading: false, isPartnerCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isPartnerCaptchaLoading: false, isPartnerCaptchaSend: false));
      Logger.error('Error :: $e');
    }
  }

  void _onPartnerReCaptchaSend(PartnerReCaptchaSend event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isPartnerCaptchaLoading: true, isPartnerCaptchaSend: false));
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
      final RecaptchaModel response = await _businessUserKycRepository.reGenerateCaptcha(sessionId: sessionId);
      if (response.code == 200) {
        emit(
          state.copyWith(
            isPartnerCaptchaSend: true,
            isPartnerCaptchaLoading: false,
            partnerCaptchaImage: response.data?.captcha ?? '',
          ),
        );
        state.partnerCaptchaInputController.clear();
      } else {
        emit(state.copyWith(isPartnerCaptchaLoading: false, isPartnerCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isPartnerCaptchaLoading: false, isPartnerCaptchaSend: false));
      Logger.error(e.toString());
    }
  }

  // ================= PROPRIETOR AADHAAR HANDLERS =================
  void _onProprietorSendAadharOtp(ProprietorSendAadharOtp event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      state.proprietorAadharOtpController.clear();
      emit(
        state.copyWith(isProprietorOtpSent: false, isProprietorOtpLoading: true, proprietorIsAadharOTPInvalidate: ''),
      );
      AadharOTPSendModel response = await _businessUserKycRepository.generateAadharOTP(
        aadhaarNumber: event.aadhar.replaceAll("-", ""),
        captcha: event.captcha,
        sessionId: event.sessionId,
      );
      if (response.code == 200) {
        emit(state.copyWith(isProprietorOtpSent: true, isProprietorOtpLoading: false));
        add(ProprietorAadharSendOtpPressed());
      } else {
        AppToast.show(message: response.message ?? '', type: ToastificationType.error);
        emit(state.copyWith(isProprietorOtpLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isProprietorOtpSent: false));
    }
  }

  void _onProprietorChangeOtpSentStatus(
    ProprietorChangeOtpSentStatus event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isProprietorOtpSent: event.isOtpSent));
  }

  void _onProprietorAadharSendOtpPressed(
    ProprietorAadharSendOtpPressed event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(isProprietorAadharOtpTimerRunning: true, proprietorAadharOtpRemainingTime: initialTime));
    Timer.periodic(Duration(seconds: 1), (timer) {
      final newTime = state.proprietorAadharOtpRemainingTime - 1;
      if (newTime <= 0) {
        timer.cancel();
        add(ProprietorAadharOtpTimerTicked(0));
      } else {
        add(ProprietorAadharOtpTimerTicked(newTime));
      }
    });
  }

  void _onProprietorAadharOtpTimerTicked(
    ProprietorAadharOtpTimerTicked event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(
      state.copyWith(
        proprietorAadharOtpRemainingTime: event.remainingTime,
        isProprietorAadharOtpTimerRunning: event.remainingTime > 0,
      ),
    );
  }

  void _onProprietorAadharNumbeVerified(
    ProprietorAadharNumbeVerified event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isProprietorAadharVerifiedLoading: true, proprietorIsAadharOTPInvalidate: null));
    try {
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';
      final AadharOTPVerifyModel response = await _businessUserKycRepository.validateAadharOtp(
        faker: false,
        otp: event.otp,
        sessionId: sessionId,
        userId: userId,
      );
      if (response.code == 200) {
        emit(
          state.copyWith(
            isProprietorAadharVerifiedLoading: false,
            isProprietorAadharVerified: true,
            proprietorAadharNumber: event.aadharNumber,
          ),
        );
      } else {
        emit(
          state.copyWith(
            isProprietorAadharVerifiedLoading: false,
            proprietorIsAadharOTPInvalidate: response.message.toString(),
          ),
        );
      }
    } catch (e) {
      emit(state.copyWith(isProprietorAadharVerifiedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onProprietorFrontSlideAadharCardUpload(
    ProprietorFrontSlideAadharCardUpload event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(proprietorFrontSideAdharFile: event.fileData));
  }

  void _onProprietorBackSlideAadharCardUpload(
    ProprietorBackSlideAadharCardUpload event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(proprietorBackSideAdharFile: event.fileData));
  }

  void _onProprietorAadharFileUploadSubmitted(
    ProprietorAadharFileUploadSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isProprietorAadharFileUploading: true));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);
    try {
      final response = await _businessUserKycRepository.uploadbusinessKyc(
        userID: userId ?? '',
        documentType: 'Aadhaar',
        documentNumber: state.proprietorAadharNumberController.text.replaceAll("-", ""),
        documentFrontImage: event.frontAadharFileData!,
        documentBackImage: event.backAadharFileData,
        isAddharCard: true,
        nameOnPan: '',
        userType: 'business',
        kycRole: "PROPRIETOR",
      );
      if (response.success == true) {
        final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
        if (nextStep != null) {
          add(KycStepChanged(nextStep));
        }
        emit(state.copyWith(isProprietorAadharFileUploading: false));
      } else {
        emit(state.copyWith(isProprietorAadharFileUploading: false));
      }
    } catch (e) {
      emit(state.copyWith(isProprietorAadharFileUploading: false));
      Logger.error(e.toString());
    }
  }

  void _onProprietorCaptchaSend(ProprietorCaptchaSend event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isProprietorCaptchaLoading: true, isProprietorCaptchaSend: false));
      final CaptchaModel response = await _businessUserKycRepository.generateCaptcha();
      if (response.code == 200) {
        emit(
          state.copyWith(
            isProprietorCaptchaSend: true,
            isProprietorCaptchaLoading: false,
            proprietorCaptchaImage: response.data?.captcha ?? '',
          ),
        );
        await Prefobj.preferences.put(Prefkeys.sessionId, response.data?.sessionId ?? '');
      } else {
        emit(state.copyWith(isProprietorCaptchaLoading: false, isProprietorCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isProprietorCaptchaLoading: false, isProprietorCaptchaSend: false));
      Logger.error('Error :: $e');
    }
  }

  void _onProprietorReCaptchaSend(ProprietorReCaptchaSend event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isProprietorCaptchaLoading: true, isProprietorCaptchaSend: false));
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
      final RecaptchaModel response = await _businessUserKycRepository.reGenerateCaptcha(sessionId: sessionId);
      if (response.code == 200) {
        emit(
          state.copyWith(
            isProprietorCaptchaSend: true,
            isProprietorCaptchaLoading: false,
            proprietorCaptchaImage: response.data?.captcha ?? '',
          ),
        );
        state.proprietorCaptchaInputController.clear();
      } else {
        emit(state.copyWith(isProprietorCaptchaLoading: false, isProprietorCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isProprietorCaptchaLoading: false, isProprietorCaptchaSend: false));
      Logger.error(e.toString());
    }
  }

  void _onDirectorAadharNumberChanged(
    DirectorAadharNumberChanged event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(
      state.copyWith(
        isDirectorCaptchaSend: false,
        isOtpSent: false,
        directorCaptchaImage: null,
        directorCaptchaInputController: TextEditingController(),
        aadharOtpController: TextEditingController(),
        isAadharVerified: false,
        // Reset any other relevant fields
      ),
    );
  }

  void _onKartaAadharNumberChanged(KartaAadharNumberChanged event, Emitter<BusinessAccountSetupState> emit) async {
    emit(
      state.copyWith(
        isKartaCaptchaSend: false,
        isKartaOtpSent: false,
        kartaCaptchaImage: null,
        kartaCaptchaInputController: TextEditingController(),
        kartaAadharOtpController: TextEditingController(),
        isKartaAadharVerified: false,
        // Reset any other relevant fields
      ),
    );
  }

  void _onPartnerAadharNumberChanged(PartnerAadharNumberChanged event, Emitter<BusinessAccountSetupState> emit) async {
    emit(
      state.copyWith(
        isPartnerCaptchaSend: false,
        isPartnerOtpSent: false,
        partnerCaptchaImage: null,
        partnerCaptchaInputController: TextEditingController(),
        partnerAadharOtpController: TextEditingController(),
        isPartnerAadharVerified: false,
        // Reset any other relevant fields
      ),
    );
  }

  void _onProprietorAadharNumberChanged(
    ProprietorAadharNumberChanged event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(
      state.copyWith(
        isProprietorCaptchaSend: false,
        isProprietorOtpSent: false,
        proprietorCaptchaImage: null,
        proprietorCaptchaInputController: TextEditingController(),
        proprietorAadharOtpController: TextEditingController(),
        isProprietorAadharVerified: false,
        // Reset any other relevant fields
      ),
    );
  }

  @override
  Future<void> close() async {
    _timer?.cancel();
    _aadhartimer?.cancel();
    _kartaAadhartimer?.cancel();
    return super.close();
  }

  Future<void> _onLoadBusinessKycFromLocal(
    LoadBusinessKycFromLocal event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    add(ResetData());
    await Future.delayed(Duration.zero); // Ensure the event is processed first
    emit(state.copyWith(isLocalDataLoading: true));
    final userJson = await Prefobj.preferences.get(Prefkeys.userDetail);
    if (userJson != null) {
      final userData = jsonDecode(userJson);
      final businessDetails = userData['business_details'] ?? {};
      final userIdentityDocs = userData['user_identity_documents'] as List? ?? [];
      final businessLegalDocs = userData['user_business_legal_documents'] as List? ?? [];
      final address = userData['user_address_documents'] ?? {};
      final gst = userData['user_gst_details'] ?? {};
      // Aadhaar for all roles
      final kartaAadhaarDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Aadhaar' && doc['kyc_role'] == 'KARTA',
        orElse: () => null,
      );
      final directorAadhaarDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Aadhaar' && doc['kyc_role'] == 'DIRECTOR',
        orElse: () => null,
      );
      final partnerAadhaarDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Aadhaar' && doc['kyc_role'] == 'PARTNER',
        orElse: () => null,
      );
      final proprietorAadhaarDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Aadhaar' && doc['kyc_role'] == 'PROPRIETOR',
        orElse: () => null,
      );
      // PAN for all roles
      final businessPanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'BUSINESS',
        orElse: () => null,
      );
      final director1PanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'DIRECTOR1',
        orElse: () => null,
      );
      final director2PanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'DIRECTOR2',
        orElse: () => null,
      );
      final kartaPanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'KARTA',
        orElse: () => null,
      );
      // final partnerPanDoc = userIdentityDocs.firstWhere(
      //   (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'PARTNER',
      //   orElse: () => null,
      // );
      // final proprietorPanDoc = userIdentityDocs.firstWhere(
      //   (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'PROPRIETOR',
      //   orElse: () => null,
      // );
      final beneficialOwnerPanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'BENEFICIAL_OWNER',
        orElse: () => null,
      );
      final businessRepPanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'BUSINESS_REPRESENTATIVE',
        orElse: () => null,
      );
      // final hufPanDoc = userIdentityDocs.firstWhere(
      //   (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'HUF',
      //   orElse: () => null,
      // );
      // ICE/IEC
      final iceDoc = businessLegalDocs.firstWhere((doc) => doc['document_type'] == 'IEC', orElse: () => null);
      // COI
      final cinDoc = businessLegalDocs.firstWhere((doc) => doc['document_type'] == 'CIN', orElse: () => null);
      // LLP Agreement
      final llpDoc = businessLegalDocs.firstWhere((doc) => doc['document_type'] == 'LLPIN', orElse: () => null);
      // Partnership Deed
      final partnershipDeedDoc = businessLegalDocs.firstWhere(
        (doc) => doc['document_type'] == 'PartnershipDeed',
        orElse: () => null,
      );
      // Bank
      final bankDoc = userData['user_bank_account_documents'] ?? {};

      // Assign controllers/state
      state.businessLegalNameController.text =
          businessDetails != null ? businessDetails['business_legal_name'] ?? '' : '';
      state.pinCodeController.text = address != null ? address['pincode'] ?? '' : '';
      state.stateNameController.text = address != null ? address['state'] ?? '' : '';
      state.cityNameController.text = address != null ? address['city'] ?? '' : '';
      state.address1NameController.text = address != null ? address['address_line1'] ?? '' : '';
      state.gstNumberController.text = gst != null ? gst['gst_number'] ?? '' : '';
      state.turnOverController.text = gst != null ? gst['estimated_annual_income'] ?? '' : '';
      state.iceNumberController.text = iceDoc != null ? iceDoc['document_number'] ?? '' : '';
      state.cinNumberController.text = cinDoc != null ? cinDoc['document_number'] ?? '' : '';
      state.llpinNumberController.text = llpDoc != null ? llpDoc['document_number'] ?? '' : '';
      state.bankAccountNumberController.text = bankDoc != null ? bankDoc['account_number'] ?? '' : '';
      state.ifscCodeController.text = bankDoc != null ? bankDoc['ifsc_code'] ?? '' : '';
      state.kartaAadharNumberController.text = kartaAadhaarDoc != null ? kartaAadhaarDoc['document_number'] ?? '' : '';
      state.partnerAadharNumberController.text =
          partnerAadhaarDoc != null ? partnerAadhaarDoc['document_number'] ?? '' : '';
      state.proprietorAadharNumberController.text =
          proprietorAadhaarDoc != null ? proprietorAadhaarDoc['document_number'] ?? '' : '';
      state.aadharNumberController.text = directorAadhaarDoc != null ? directorAadhaarDoc['document_number'] ?? '' : '';

      // Download and assign files (Aadhaar, PAN, GST, address, ICE, COI, LLP, Partnership, Bank)
      // Aadhaar
      if (kartaAadhaarDoc != null && kartaAadhaarDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(kartaAadhaarDoc['front_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(kartaFrontSideAdharFile: fileData));
        }
      }
      if (kartaAadhaarDoc != null && kartaAadhaarDoc['back_doc_url'] != null) {
        final fileData = await getFileDataFromPath(kartaAadhaarDoc['back_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(kartaBackSideAdharFile: fileData));
        }
      }

      if (directorAadhaarDoc != null && directorAadhaarDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(directorAadhaarDoc['front_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(frontSideAdharFile: fileData));
        }
      }
      if (directorAadhaarDoc != null && directorAadhaarDoc['back_doc_url'] != null) {
        final fileData = await getFileDataFromPath(directorAadhaarDoc['back_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(backSideAdharFile: fileData));
        }
      }
      if (partnerAadhaarDoc != null && partnerAadhaarDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(partnerAadhaarDoc['front_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(partnerFrontSideAdharFile: fileData));
        }
      }
      if (partnerAadhaarDoc != null && partnerAadhaarDoc['back_doc_url'] != null) {
        final fileData = await getFileDataFromPath(partnerAadhaarDoc['back_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(partnerBackSideAdharFile: fileData));
        }
      }
      if (proprietorAadhaarDoc != null && proprietorAadhaarDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(proprietorAadhaarDoc['front_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(proprietorFrontSideAdharFile: fileData));
        }
      }
      if (proprietorAadhaarDoc != null && proprietorAadhaarDoc['back_doc_url'] != null) {
        final fileData = await getFileDataFromPath(proprietorAadhaarDoc['back_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(proprietorBackSideAdharFile: fileData));
        }
      }
      // PAN
      if (businessPanDoc != null && businessPanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(businessPanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(businessPanCardFile: fileData));
        }
      }
      if (director1PanDoc != null && director1PanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(director1PanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(director1PanCardFile: fileData));
        }
      }
      if (director2PanDoc != null && director2PanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(director2PanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(director2PanCardFile: fileData));
        }
      }
      if (kartaPanDoc != null && kartaPanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(kartaPanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(hufPanCardFile: fileData));
        }
      }
      // partnerPanCardFile and proprietorPanCardFile do not exist in state, so skip them
      if (beneficialOwnerPanDoc != null && beneficialOwnerPanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(beneficialOwnerPanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(beneficialOwnerPanCardFile: fileData));
        }
      }
      if (businessRepPanDoc != null && businessRepPanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(businessRepPanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(businessRepresentativePanCardFile: fileData));
        }
      }
      // GST Certificate
      if (gst != null && gst['gst_certificate_url'] != '') {
        final fileData = await getFileDataFromPath(gst['gst_certificate_url'], 'GST');
        if (fileData != null) {
          emit(state.copyWith(gstCertificateFile: fileData));
        }
      }
      // Address
      if (address != null && address['front_doc_url'] != '') {
        final fileData = await getFileDataFromPath(address['front_doc_url'], address['document_type'] ?? 'Address');
        if (fileData != null) {
          emit(state.copyWith(addressVerificationFile: fileData));
        }
      }
      // ICE Certificate
      if (iceDoc != null && iceDoc['doc_url'] != null) {
        final fileData = await getFileDataFromPath(iceDoc['doc_url'], "ICE");
        if (fileData != null) {
          emit(state.copyWith(iceCertificateFile: fileData));
        }
      }
      // COI
      if (cinDoc != null && cinDoc['doc_url'] != null) {
        final fileData = await getFileDataFromPath(cinDoc['doc_url'], "CIN");
        if (fileData != null) {
          emit(state.copyWith(coiCertificateFile: fileData));
        }
      }
      // LLP Agreement
      if (llpDoc != null && llpDoc['doc_url'] != null) {
        final fileData = await getFileDataFromPath(llpDoc['doc_url'], "LLPIN");
        if (fileData != null) {
          emit(state.copyWith(uploadLLPAgreementFile: fileData));
        }
      }
      // Partnership Deed
      if (partnershipDeedDoc != null && partnershipDeedDoc['doc_url'] != null) {
        final fileData = await getFileDataFromPath(partnershipDeedDoc['doc_url'], "PartnershipDeed");
        if (fileData != null) {
          emit(state.copyWith(uploadPartnershipDeed: fileData));
        }
      }
      // Bank Verification File
      if (bankDoc != null && bankDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(bankDoc['front_doc_url'], 'Bank');
        if (fileData != null) {
          emit(state.copyWith(bankVerificationFile: fileData));
        }
      }

      // Step skipping logic
      KycVerificationSteps nextKycStep = state.currentKycVerificationStep;
      // 1. Aadhaar (at least one role must be present and have file)
      final hasAadhaar =
          kartaAadhaarDoc != null ||
          directorAadhaarDoc != null ||
          partnerAadhaarDoc != null ||
          proprietorAadhaarDoc != null;
      final hasAadhaarFile =
          state.kartaFrontSideAdharFile != null ||
          state.frontSideAdharFile != null ||
          state.partnerFrontSideAdharFile != null ||
          state.proprietorFrontSideAdharFile != null;

      final hasVerifyDoc = cinDoc != null || llpDoc != null || partnershipDeedDoc != null;

      final hasVerifyDocFile =
          state.coiCertificateFile != null ||
          state.uploadLLPAgreementFile != null ||
          state.uploadPartnershipDeed != null;

      if (!hasAadhaar || !hasAadhaarFile) {
        nextKycStep = KycVerificationSteps.aadharVerfication;
      } else if (state.businessPanCardFile == null || state.businessPanNumberController.text.isEmpty) {
        nextKycStep = KycVerificationSteps.panVerification;
      } else if (state.addressVerificationFile == null || state.pinCodeController.text.isEmpty) {
        nextKycStep = KycVerificationSteps.registeredOfficeAddress;
      } else if (state.turnOverController.text.isEmpty) {
        nextKycStep = KycVerificationSteps.annualTurnoverDeclaration;
      } else if (state.iceCertificateFile == null || state.iceNumberController.text.isEmpty) {
        nextKycStep = KycVerificationSteps.iecVerification;
      } else if (hasVerifyDoc || hasVerifyDocFile) {
        nextKycStep = KycVerificationSteps.companyIncorporationVerification;
      } else if (state.bankVerificationFile == null || state.bankAccountNumberController.text.isEmpty) {
        nextKycStep = KycVerificationSteps.bankAccountLinking;
      } else {
        nextKycStep = KycVerificationSteps.contactInformation;
      }

      emit(
        state.copyWith(
          businessLegalNameController: state.businessLegalNameController,
          businessNatureString: state.businessNatureString,
          selectedBusinessEntityType: state.selectedBusinessEntityType,
          selectedbusinessGoodsExportType: state.selectedbusinessGoodsExportType,
          kartaAadharNumberController: state.kartaAadharNumberController,
          pinCodeController: state.pinCodeController,
          stateNameController: state.stateNameController,
          cityNameController: state.cityNameController,
          address1NameController: state.address1NameController,
          gstNumberController: state.gstNumberController,
          turnOverController: state.turnOverController,
          iceNumberController: state.iceNumberController,
          cinNumberController: state.cinNumberController,
          llpinNumberController: state.llpinNumberController,
          bankAccountNumberController: state.bankAccountNumberController,
          ifscCodeController: state.ifscCodeController,
          currentKycVerificationStep: nextKycStep,
          isKartaAadharVerified: kartaAadhaarDoc != null ? true : false,
          kartaAadharNumber: kartaAadhaarDoc != null ? kartaAadhaarDoc['document_number'] ?? '' : '',
          partnerAadharNumberController: state.partnerAadharNumberController,
          isPartnerAadharVerified: partnerAadhaarDoc != null ? true : false,
          partnerAadharNumber: partnerAadhaarDoc != null ? partnerAadhaarDoc['document_number'] ?? '' : '',
          proprietorAadharNumberController: state.proprietorAadharNumberController,
          isProprietorAadharVerified: proprietorAadhaarDoc != null ? true : false,
          proprietorAadharNumber: proprietorAadhaarDoc != null ? proprietorAadhaarDoc['document_number'] ?? '' : '',
          aadharNumberController: state.aadharNumberController,
          isAadharVerified: directorAadhaarDoc != null ? true : false,
          aadharNumber: directorAadhaarDoc != null ? directorAadhaarDoc['document_number'] ?? '' : '',
          isGstCertificateMandatory:
              gst != null ? (gst['estimated_annual_income'].contains("Less than") ? false : true) : false,
          isGSTNumberVerify: gst['gst_number'] != '' ? true : false,
          selectedAddressVerificationDocType:
              address != null
                  ? (address['document_type'] ?? '').replaceAllMapped(
                    RegExp(r'(?<!^)([A-Z])'),
                    (match) => ' ${match.group(1)}',
                  )
                  : '',
          selectedAnnualTurnover: gst['estimated_annual_income'],
          gstLegalName: gst['legal_name'],
        ),
      );
      emit(state.copyWith(isLocalDataLoading: false));
      event.completer?.complete();
    }
  }

  Future<FileData?> getFileDataFromPath(String path, String fallbackName) async {
    try {
      final response = await _personalUserKycRepository.getPresignedUrl(urlPath: path);
      if (response.url != null) {
        Logger.success(response.url);
        // Extract the real file name from the URL
        final fileName = _extractFileNameFromUrl(response.url!) ?? fallbackName;
        return await downloadFileDataFromUrl(response.url!, fileName);
      }
    } catch (e) {
      Logger.error(e.toString());
    }
    return null;
  }

  Future<FileData?> downloadFileDataFromUrl(String url, String name) async {
    try {
      // Validate URL
      if (url.isEmpty) {
        Logger.error('Download error: Empty URL provided');
        return null;
      }

      final response = await Dio().get<List<int>>(
        url,
        options: Options(
          responseType: ResponseType.bytes,
          validateStatus: (status) => status != null && status < 500,
          receiveTimeout: const Duration(seconds: 30),
          // sendTimeout: const Duration(seconds: 30),
        ),
      );

      // Check if response is successful
      if (response.statusCode != 200) {
        Logger.error('Download error: HTTP ${response.statusCode} for URL: $url');
        return null;
      }

      // Validate response data
      if (response.data == null || response.data!.isEmpty) {
        Logger.error('Download error: Empty response data for URL: $url');
        return null;
      }

      // Validate data is actually bytes
      if (response.data is! List<int>) {
        Logger.error('Download error: Invalid response type for URL: $url');
        return null;
      }

      return FileData(
        name: name,
        bytes: Uint8List.fromList(response.data!),
        path: url,
        sizeInMB: response.data!.length / (1024 * 1024),
      );
    } on DioException catch (e) {
      Logger.error('Download error: DioException - ${e.message} for URL: $url');
      return null;
    } catch (e) {
      Logger.error('Download error: Unexpected error - $e for URL: $url');
      return null;
    }
  }

  String? _extractFileNameFromUrl(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return null;
    return uri.pathSegments.isNotEmpty ? uri.pathSegments.last : null;
  }

  Future<void> _refreshKycFileData() async {
    final userJson = await Prefobj.preferences.get(Prefkeys.userDetail);
    if (userJson != null) {
      final userData = jsonDecode(userJson);
      final userIdentityDocs = userData['user_identity_documents'] as List? ?? [];
      final businessLegalDocs = userData['user_business_legal_documents'] as List? ?? [];
      final address = userData['user_address_documents'] ?? {};
      final gst = userData['user_gst_details'] ?? {};

      final partnershipDeedDoc = businessLegalDocs.firstWhere(
        (doc) => doc['document_type'] == 'PARTNERSHIP_DEED',
        orElse: () => null,
      );
      // Bank
      final bankDoc = userData['user_bank_account_documents'] ?? {};

      // ICE/IEC
      final iceDoc = businessLegalDocs.firstWhere((doc) => doc['document_type'] == 'IEC', orElse: () => null);
      // COI
      final coiDoc = businessLegalDocs.firstWhere((doc) => doc['document_type'] == 'COI', orElse: () => null);
      // LLP Agreement
      final llpDoc = businessLegalDocs.firstWhere((doc) => doc['document_type'] == 'LLP_AGREEMENT', orElse: () => null);
      // Partnership Deed

      final kartaAadhaarDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Aadhaar' && doc['kyc_role'] == 'KARTA',
        orElse: () => null,
      );
      final directorAadhaarDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Aadhaar' && doc['kyc_role'] == 'DIRECTOR',
        orElse: () => null,
      );
      final partnerAadhaarDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Aadhaar' && doc['kyc_role'] == 'PARTNER',
        orElse: () => null,
      );
      final proprietorAadhaarDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Aadhaar' && doc['kyc_role'] == 'PROPRIETOR',
        orElse: () => null,
      );
      // PAN for all roles
      final businessPanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'BUSINESS',
        orElse: () => null,
      );
      final director1PanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'DIRECTOR1',
        orElse: () => null,
      );
      final director2PanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'DIRECTOR2',
        orElse: () => null,
      );
      final kartaPanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'KARTA',
        orElse: () => null,
      );
      // final partnerPanDoc = userIdentityDocs.firstWhere(
      //   (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'PARTNER',
      //   orElse: () => null,
      // );
      // final proprietorPanDoc = userIdentityDocs.firstWhere(
      //   (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'PROPRIETOR',
      //   orElse: () => null,
      // );
      final beneficialOwnerPanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'BENEFICIAL_OWNER',
        orElse: () => null,
      );
      final businessRepPanDoc = userIdentityDocs.firstWhere(
        (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'BUSINESS_REPRESENTATIVE',
        orElse: () => null,
      );
      // final hufPanDoc = userIdentityDocs.firstWhere(
      //   (doc) => doc['document_type'] == 'Pan' && doc['kyc_role'] == 'HUF',
      //   orElse: () => null,
      // );

      if (kartaAadhaarDoc != null && kartaAadhaarDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(kartaAadhaarDoc['front_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(kartaFrontSideAdharFile: fileData));
        }
      }
      if (kartaAadhaarDoc != null && kartaAadhaarDoc['back_doc_url'] != null) {
        final fileData = await getFileDataFromPath(kartaAadhaarDoc['back_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(kartaBackSideAdharFile: fileData));
        }
      }

      if (directorAadhaarDoc != null && directorAadhaarDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(directorAadhaarDoc['front_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(frontSideAdharFile: fileData));
        }
      }
      if (directorAadhaarDoc != null && directorAadhaarDoc['back_doc_url'] != null) {
        final fileData = await getFileDataFromPath(directorAadhaarDoc['back_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(backSideAdharFile: fileData));
        }
      }
      if (partnerAadhaarDoc != null && partnerAadhaarDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(partnerAadhaarDoc['front_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(partnerFrontSideAdharFile: fileData));
        }
      }
      if (partnerAadhaarDoc != null && partnerAadhaarDoc['back_doc_url'] != null) {
        final fileData = await getFileDataFromPath(partnerAadhaarDoc['back_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(partnerBackSideAdharFile: fileData));
        }
      }
      if (proprietorAadhaarDoc != null && proprietorAadhaarDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(proprietorAadhaarDoc['front_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(proprietorFrontSideAdharFile: fileData));
        }
      }
      if (proprietorAadhaarDoc != null && proprietorAadhaarDoc['back_doc_url'] != null) {
        final fileData = await getFileDataFromPath(proprietorAadhaarDoc['back_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(proprietorBackSideAdharFile: fileData));
        }
      }
      // PAN
      if (businessPanDoc != null && businessPanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(businessPanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(businessPanCardFile: fileData));
        }
      }
      if (director1PanDoc != null && director1PanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(director1PanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(director1PanCardFile: fileData));
        }
      }
      if (director2PanDoc != null && director2PanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(director2PanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(director2PanCardFile: fileData));
        }
      }
      if (kartaPanDoc != null && kartaPanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(kartaPanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(hufPanCardFile: fileData));
        }
      }
      // partnerPanCardFile and proprietorPanCardFile do not exist in state, so skip them
      if (beneficialOwnerPanDoc != null && beneficialOwnerPanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(beneficialOwnerPanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(beneficialOwnerPanCardFile: fileData));
        }
      }
      if (businessRepPanDoc != null && businessRepPanDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(businessRepPanDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(businessRepresentativePanCardFile: fileData));
        }
      }
      // GST Certificate
      if (gst != null && gst['gst_certificate_url'] != '') {
        final fileData = await getFileDataFromPath(gst['gst_certificate_url'], 'GST');
        if (fileData != null) {
          emit(state.copyWith(gstCertificateFile: fileData));
        }
      }
      // Address
      if (address != null && address['front_doc_url'] != '') {
        final fileData = await getFileDataFromPath(address['front_doc_url'], address['document_type'] ?? 'Address');
        if (fileData != null) {
          emit(state.copyWith(addressVerificationFile: fileData));
        }
      }
      // ICE Certificate
      if (iceDoc != null && iceDoc['doc_url'] != null) {
        final fileData = await getFileDataFromPath(iceDoc['doc_url'], "ICE");
        if (fileData != null) {
          emit(state.copyWith(iceCertificateFile: fileData));
        }
      }
      // COI
      if (coiDoc != null && coiDoc['doc_url'] != null) {
        final fileData = await getFileDataFromPath(coiDoc['doc_url'], "COI");
        if (fileData != null) {
          emit(state.copyWith(coiCertificateFile: fileData));
        }
      }
      // LLP Agreement
      if (llpDoc != null && llpDoc['doc_url'] != null) {
        final fileData = await getFileDataFromPath(llpDoc['doc_url'], "LLP_AGREEMENT");
        if (fileData != null) {
          emit(state.copyWith(uploadLLPAgreementFile: fileData));
        }
      }
      // Partnership Deed
      if (partnershipDeedDoc != null && partnershipDeedDoc['doc_url'] != null) {
        final fileData = await getFileDataFromPath(partnershipDeedDoc['doc_url'], "PARTNERSHIP_DEED");
        if (fileData != null) {
          emit(state.copyWith(uploadPartnershipDeed: fileData));
        }
      }
      // Bank Verification File
      if (bankDoc != null && bankDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(bankDoc['front_doc_url'], 'Bank');
        if (fileData != null) {
          emit(state.copyWith(bankVerificationFile: fileData));
        }
      }
    }
  }

  Future<void> _onBusinessGetCityAndState(
    BusinessGetCityAndState event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isCityAndStateLoading: true, isCityAndStateVerified: false));
    try {
      final GetCityAndStateModel response = await _personalUserKycRepository.getCityAndState(pincode: event.pinCode);
      if (response.success == true) {
        emit(
          state.copyWith(
            isCityAndStateLoading: false,
            cityNameController: TextEditingController(text: response.data?.city ?? ''),
            stateNameController: TextEditingController(text: response.data?.state ?? ''),
            isCityAndStateVerified: true,
          ),
        );
      } else {
        emit(state.copyWith(isCityAndStateLoading: false, isCityAndStateVerified: false));
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(state.copyWith(isCityAndStateLoading: false, isCityAndStateVerified: false));
    }
  }

  void _onLLPINVerificationSubmitted(LLPINVerificationSubmitted event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isCINVerifyingLoading: true));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);
    try {
      final response = await _personalUserKycRepository.uploadBusinessLegalDocuments(
        userID: userId ?? '',
        userType: 'business',
        documentType: 'LLPIN',
        documentNumber: event.llpinNumber ?? '',
        documentFrontImage: event.coifile,
        documentbackImage: event.llpfile,
      );
      if (response.success == true) {
        final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
        if (nextStep != null) {
          add(KycStepChanged(nextStep));
        }
        emit(state.copyWith(isCINVerifyingLoading: false));
      } else {
        emit(state.copyWith(isCINVerifyingLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isCINVerifyingLoading: false));
    }
  }

  void _onPartnerShipDeedVerificationSubmitted(
    PartnerShipDeedVerificationSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isCINVerifyingLoading: true));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);
    try {
      final response = await _personalUserKycRepository.uploadBusinessLegalDocuments(
        userID: userId ?? '',
        userType: 'business',
        documentType: 'PartnershipDeed',
        documentFrontImage: event.partnerShipDeedDoc,
      );
      if (response.success == true) {
        final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
        if (nextStep != null) {
          add(KycStepChanged(nextStep));
        }
        emit(state.copyWith(isCINVerifyingLoading: false));
      } else {
        emit(state.copyWith(isCINVerifyingLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isCINVerifyingLoading: false));
    }
  }

  void _onBusinessGSTVerification(BusinessGSTVerification event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isGstVerificationLoading: true));

      final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

      final response = await _personalUserKycRepository.getGSTDetails(
        userID: userId ?? '',
        estimatedAnnualIncome: event.turnover,
        gstNumber: event.gstNumber,
      );
      if (response.success == true) {
        emit(
          state.copyWith(
            isGstVerificationLoading: false,
            gstLegalName: response.data?.legalName ?? '',
            isGSTNumberVerify: true,
          ),
        );
      } else {
        emit(state.copyWith(isGstVerificationLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isGstVerificationLoading: false));
    }
  }

  Future<void> _onGetHUFPanDetails(GetHUFPanDetails event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isHUFPanDetailsLoading: true, isHUFPanDetailsVerified: false));
    try {
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';
      final GetPanDetailModel response = await _personalUserKycRepository.getPanDetails(
        panNumber: event.panNumber,
        userId: userId,
      );
      if (response.success == true) {
        final String panName = response.data?.nameInformation?.panNameCleaned ?? '';
        emit(state.copyWith(isHUFPanDetailsLoading: false, fullHUFNamePan: panName, isHUFPanDetailsVerified: true));
      } else {
        emit(state.copyWith(isHUFPanDetailsLoading: false, isHUFPanDetailsVerified: false));
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(state.copyWith(isHUFPanDetailsLoading: false, isHUFPanDetailsVerified: false));
    }
  }

  void _onHUFPanNumberChanged(HUFPanNumberChanged event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(isHUFPanDetailsVerified: false));
  }

  Future<void> _onGetDirector1PanDetails(GetDirector1PanDetails event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isDirector1PanDetailsLoading: true, isDirector1PanDetailsVerified: false));
    try {
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';
      final GetPanDetailModel response = await _personalUserKycRepository.getPanDetails(
        panNumber: event.panNumber,
        userId: userId,
      );
      if (response.success == true) {
        final String panName = response.data?.nameInformation?.panNameCleaned ?? '';
        emit(
          state.copyWith(
            isDirector1PanDetailsLoading: false,
            fullDirector1NamePan: panName,
            isDirector1PanDetailsVerified: true,
          ),
        );
      } else {
        emit(state.copyWith(isDirector1PanDetailsLoading: false, isDirector1PanDetailsVerified: false));
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(state.copyWith(isDirector1PanDetailsLoading: false, isDirector1PanDetailsVerified: false));
    }
  }

  void _onDirector1PanNumberChanged(Director1PanNumberChanged event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(isDirector1PanDetailsVerified: false));
  }

  Future<void> _onGetDirector2PanDetails(GetDirector2PanDetails event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isDirector2PanDetailsLoading: true, isDirector2PanDetailsVerified: false));
    try {
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';
      final GetPanDetailModel response = await _personalUserKycRepository.getPanDetails(
        panNumber: event.panNumber,
        userId: userId,
      );
      if (response.success == true) {
        final String panName = response.data?.nameInformation?.panNameCleaned ?? '';
        emit(
          state.copyWith(
            isDirector2PanDetailsLoading: false,
            fullDirector2NamePan: panName,
            isDirector2PanDetailsVerified: true,
          ),
        );
      } else {
        emit(state.copyWith(isDirector2PanDetailsLoading: false, isDirector2PanDetailsVerified: false));
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(state.copyWith(isDirector2PanDetailsLoading: false, isDirector2PanDetailsVerified: false));
    }
  }

  void _onDirector2PanNumberChanged(Director2PanNumberChanged event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(isDirector2PanDetailsVerified: false));
  }

  Future<void> _onGetBeneficialOwnerPanDetails(
    GetBeneficialOwnerPanDetails event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isBeneficialOwnerPanDetailsLoading: true, isBeneficialOwnerPanDetailsVerified: false));
    try {
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';
      final GetPanDetailModel response = await _personalUserKycRepository.getPanDetails(
        panNumber: event.panNumber,
        userId: userId,
      );
      if (response.success == true) {
        final String panName = response.data?.nameInformation?.panNameCleaned ?? '';
        emit(
          state.copyWith(
            isBeneficialOwnerPanDetailsLoading: false,
            fullBeneficialOwnerNamePan: panName,
            isBeneficialOwnerPanDetailsVerified: true,
          ),
        );
      } else {
        emit(state.copyWith(isBeneficialOwnerPanDetailsLoading: false, isBeneficialOwnerPanDetailsVerified: false));
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(state.copyWith(isBeneficialOwnerPanDetailsLoading: false, isBeneficialOwnerPanDetailsVerified: false));
    }
  }

  void _onBeneficialOwnerPanNumberChanged(
    BeneficialOwnerPanNumberChanged event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(isBeneficialOwnerPanDetailsVerified: false));
  }

  Future<void> _onGetBusinessRepresentativePanDetails(
    GetBusinessRepresentativePanDetails event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(
      state.copyWith(
        isBusinessRepresentativePanDetailsLoading: true,
        isBusinessRepresentativePanDetailsVerified: false,
      ),
    );
    try {
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';
      final GetPanDetailModel response = await _personalUserKycRepository.getPanDetails(
        panNumber: event.panNumber,
        userId: userId,
      );
      if (response.success == true) {
        final String panName = response.data?.nameInformation?.panNameCleaned ?? '';
        emit(
          state.copyWith(
            isBusinessRepresentativePanDetailsLoading: false,
            fullBusinessRepresentativeNamePan: panName,
            isBusinessRepresentativePanDetailsVerified: true,
          ),
        );
      } else {
        emit(
          state.copyWith(
            isBusinessRepresentativePanDetailsLoading: false,
            isBusinessRepresentativePanDetailsVerified: false,
          ),
        );
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(
        state.copyWith(
          isBusinessRepresentativePanDetailsLoading: false,
          isBusinessRepresentativePanDetailsVerified: false,
        ),
      );
    }
  }

  void _onBusinessRepresentativePanNumberChanged(
    BusinessRepresentativePanNumberChanged event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(isBusinessRepresentativePanDetailsVerified: false));
  }

  void _onContactInformationSubmitted(
    ContactInformationSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isContactInfoSubmitLoading: true));

    try {
      // final index = state.currentKycVerificationStep.index;
      // if (index < KycVerificationSteps.values.length - 1) {
      //   add(KycStepChanged(KycVerificationSteps.values[index + 1]));
      // }
      final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
      if (nextStep != null) {
        add(KycStepChanged(nextStep));
      }
      emit(state.copyWith(isContactInfoSubmitLoading: false));
    } catch (e) {
      emit(state.copyWith(isContactInfoSubmitLoading: false));
    }
  }

  void _onSaveOtherDirectorPanDetails(
    SaveOtherDirectorPanDetails event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isOtherDirectorPanCardSaveLoading: true, isOtherDirectorPanCardSave: false));
    try {
      await Future.delayed(Duration(seconds: 2));
      emit(state.copyWith(isOtherDirectorPanCardSaveLoading: false, isOtherDirectorPanCardSave: true));
      add(OtherDirectorKycStepChanged(OtherDirectorKycSteps.aadharDetails));
    } catch (e) {
      Logger.error('Error saving director PAN details: $e');
      emit(state.copyWith(isOtherDirectorPanCardSaveLoading: false));
    }
  }

  // Other Director Aadhar Event Handlers
  void _onOtherDirectorAadharNumberChanged(
    OtherDirectorAadharNumberChanged event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(isOtherDirectorAadharVerified: false));
  }

  Future<void> _onOtherDirectorCaptchaSend(
    OtherDirectorCaptchaSend event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    try {
      emit(state.copyWith(isOtherDirectorDirectorCaptchaLoading: true, isOtherDirectorCaptchaSend: false));
      final CaptchaModel response = await _businessUserKycRepository.generateCaptcha();
      if (response.code == 200) {
        emit(
          state.copyWith(
            isOtherDirectorCaptchaSend: true,
            isOtherDirectorDirectorCaptchaLoading: false,
            otherDirectorCaptchaImage: response.data?.captcha ?? '',
          ),
        );
        await Prefobj.preferences.put(Prefkeys.sessionId, response.data?.sessionId ?? '');
      } else {
        emit(state.copyWith(isOtherDirectorDirectorCaptchaLoading: false, isOtherDirectorCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isOtherDirectorDirectorCaptchaLoading: false, isOtherDirectorCaptchaSend: false));
      Logger.error('Error :: $e');
    }
  }

  void _onOtherDirectorReCaptchaSend(OtherDirectorReCaptchaSend event, Emitter<BusinessAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isOtherDirectorDirectorCaptchaLoading: true, isOtherDirectorCaptchaSend: false));
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';

      final RecaptchaModel response = await _businessUserKycRepository.reGenerateCaptcha(sessionId: sessionId);
      if (response.code == 200) {
        emit(
          state.copyWith(
            isOtherDirectorCaptchaSend: true,
            isOtherDirectorDirectorCaptchaLoading: false,
            otherDirectorCaptchaImage: response.data?.captcha ?? '',
          ),
        );
        state.otherDirectorCaptchaInputController.clear();
      } else {
        emit(state.copyWith(isOtherDirectorDirectorCaptchaLoading: false, isOtherDirectorCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isOtherDirectorDirectorCaptchaLoading: false, isOtherDirectorCaptchaSend: false));
      Logger.error(e.toString());
    }
  }

  Future<void> _onOtherDirectorSendAadharOtp(
    OtherDirectorSendAadharOtp event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    try {
      state.otherDirectoraadharOtpController.clear();
      emit(
        state.copyWith(
          isOtherDirectorOtpSent: false,
          isOtherDirectorAadharOtpLoading: true,
          isOtherAadharOTPInvalidate: '',
        ),
      );
      AadharOTPSendModel response = await _businessUserKycRepository.generateAadharOTP(
        aadhaarNumber: event.aadharNumber.replaceAll("-", ""),
        captcha: event.captchaInput,
        sessionId: event.sessionId,
      );
      if (response.code == 200) {
        emit(state.copyWith(isOtherDirectorOtpSent: true, isOtherDirectorAadharOtpLoading: false));
        add(AadharSendOtpPressed());
      } else {
        AppToast.show(message: response.message ?? '', type: ToastificationType.error);
        emit(state.copyWith(isOtherDirectorAadharOtpLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isOtherDirectorOtpSent: false, isOtherDirectorAadharOtpLoading: false));
    }
  }

  void _onOtherDirectorAadharSendOtpPressed(
    OtherDirectorAadharSendOtpPressed event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    _aadhartimer?.cancel();
    emit(state.copyWith(isOtherDirectorAadharOtpTimerRunning: true, otherDirectorAadharOtpRemainingTime: initialTime));
    _aadhartimer = Timer.periodic(Duration(seconds: 1), (timer) {
      final newTime = state.otherDirectorAadharOtpRemainingTime - 1;
      if (newTime <= 0) {
        timer.cancel();
        add(OtherDirectorAadharOtpTimerTicked(0));
      } else {
        add(OtherDirectorAadharOtpTimerTicked(newTime));
      }
    });
  }

  void _onOtherDirectorAadharOtpTimerTicked(
    OtherDirectorAadharOtpTimerTicked event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(
      state.copyWith(
        otherDirectorAadharOtpRemainingTime: event.remainingTime,
        isOtherDirectorAadharOtpTimerRunning: event.remainingTime > 0,
      ),
    );
  }

  void _onOtherDirectorAadharNumbeVerified(
    OtherDirectorAadharNumbeVerified event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isOtherDirectorAadharVerifiedLoading: true, isOtherAadharOTPInvalidate: null));
    try {
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';

      final AadharOTPVerifyModel response = await _businessUserKycRepository.validateAadharOtp(
        faker: false,
        otp: event.otp,
        sessionId: sessionId,
        userId: userId,
      );
      if (response.code == 200) {
        emit(
          state.copyWith(
            isOtherDirectorAadharVerifiedLoading: false,
            isOtherDirectorAadharVerified: true,
            otherDirectorAadharNumber: event.aadharNumber,
          ),
        );
        _aadhartimer?.cancel();
      } else {
        emit(
          state.copyWith(
            isOtherDirectorAadharVerifiedLoading: false,
            isOtherAadharOTPInvalidate: response.message.toString(),
          ),
        );
      }
    } catch (e) {
      emit(state.copyWith(isOtherDirectorAadharVerifiedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onOtherDirectorFrontSlideAadharCardUpload(
    OtherDirectorFrontSlideAadharCardUpload event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(otherDirectorAadharfrontSideAdharFile: event.fileData));
  }

  void _onOtherDirectorBackSlideAadharCardUpload(
    OtherDirectorBackSlideAadharCardUpload event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(otherDirectorAadharBackSideAdharFile: event.fileData));
  }

  void _onOtherDirectorAadharFileUploadSubmitted(
    OtherDirectorAadharFileUploadSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isOtherDirectorAadharFileUploading: true));
    try {
      await Future.delayed(Duration(seconds: 2));
      emit(state.copyWith(isOtherDirectorAadharFileUploading: false));
    } catch (e) {
      Logger.error('Error uploading Aadhar files: $e');
      emit(state.copyWith(isOtherDirectorAadharFileUploading: false));
    }
  }

  void _onOtherDirectorShowDialogWidthoutAadharUpload(
    OtherDirectorShowDialogWidthoutAadharUpload event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isOtherDirectorAadharFileUploading: true));
    try {
      await Future.delayed(Duration(seconds: 2));
      emit(state.copyWith(isOtherDirectorAadharFileUploading: false));
    } catch (e) {
      Logger.error('Error uploading Aadhar files: $e');
      emit(state.copyWith(isOtherDirectorAadharFileUploading: false));
    }
  }

  void _onDirectorKycStepChange(DirectorKycStepChanged event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(directorKycStep: event.stepIndex));
  }

  void _onOtherDirectorKycStepChange(OtherDirectorKycStepChanged event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(otherDirectorKycStep: event.stepIndex));
  }

  void _onShowBusinessRepresentativeSelectionDialog(
    ShowBusinessRepresentativeSelectionDialog event,
    Emitter<BusinessAccountSetupState> emit,
  ) {
    emit(state.copyWith(showBusinessRepresentativeSelectionDialog: true));
  }

  void _onSelectBusinessRepresentative(SelectBusinessRepresentative event, Emitter<BusinessAccountSetupState> emit) {
    emit(
      state.copyWith(
        selectedBusinessRepresentativeOption: event.selectedOption,
        showBusinessRepresentativeSelectionDialog: false,
      ),
    );
  }

  void _onConfirmBusinessRepresentativeAndNextStep(
    ConfirmBusinessRepresentativeAndNextStep event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isBusinessRepresentativeConfirmLoading: true, isOtherDirectorKycVerify: false));
    try {
      // TODO: Replace with actual API call
      await Future.delayed(Duration(seconds: 2));
      emit(state.copyWith(isBusinessRepresentativeConfirmLoading: false, isOtherDirectorKycVerify: true));
    } catch (e) {
      emit(state.copyWith(isBusinessRepresentativeConfirmLoading: false));
      Logger.error(e.toString());
    }
  }

  Future<void> _onGetCompanyPanDetails(GetCompanyPanDetails event, Emitter<BusinessAccountSetupState> emit) async {
    emit(state.copyWith(isCompanyPanDetailsLoading: true, isCompanyPanDetailsVerified: false));
    try {
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';
      final GetPanDetailModel response = await _personalUserKycRepository.getPanDetails(
        panNumber: event.panNumber,
        userId: userId,
      );
      if (response.success == true) {
        final String panName = response.data?.nameInformation?.panNameCleaned ?? '';
        emit(
          state.copyWith(
            isCompanyPanDetailsLoading: false,
            fullCompanyNamePan: panName,
            isCompanyPanDetailsVerified: true,
          ),
        );
      } else {
        emit(state.copyWith(isCompanyPanDetailsLoading: false, isCompanyPanDetailsVerified: false));
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(state.copyWith(isCompanyPanDetailsLoading: false, isCompanyPanDetailsVerified: false));
    }
  }

  void _onCompanyPanNumberChanged(CompanyPanNumberChanged event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(isCompanyPanDetailsVerified: false));
  }

  void _onUploadCompanyPanCard(UploadCompanyPanCard event, Emitter<BusinessAccountSetupState> emit) {
    emit(state.copyWith(companyPanCardFile: event.fileData));
  }

  void _onCompanyPanVerificationSubmitted(
    CompanyPanVerificationSubmitted event,
    Emitter<BusinessAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isCompanyPanVerifyingLoading: true));
    try {
      await Future.delayed(Duration(seconds: 2));
      final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
      if (nextStep != null) {
        add(KycStepChanged(nextStep));
      }
      emit(state.copyWith(isCompanyPanVerifyingLoading: false));
    } catch (e) {
      emit(state.copyWith(isCompanyPanVerifyingLoading: false));
    }
  }

  /// Helper method to get the next KYC step based on business type
  Future<KycVerificationSteps?> _getNextKycStep(KycVerificationSteps currentStep) async {
    try {
      final businessType = await _getBusinessType();

      List<KycVerificationSteps> steps;
      if (businessType == 'Company') {
        steps = [
          KycVerificationSteps.aadharVerfication,
          KycVerificationSteps.companyIncorporationVerification,
          KycVerificationSteps.annualTurnoverDeclaration,
          KycVerificationSteps.iecVerification,
          KycVerificationSteps.panVerification,
          KycVerificationSteps.contactInformation,
          KycVerificationSteps.registeredOfficeAddress,
          KycVerificationSteps.bankAccountLinking,
        ];
      } else if (businessType == 'LLP (Limited Liability Partnership)' || businessType == 'Partnership Firm') {
        steps = KycVerificationSteps.values.where((step) => step != KycVerificationSteps.contactInformation).toList();
      } else if (businessType == 'HUF (Hindu Undivided Family)') {
        steps = [
          KycVerificationSteps.aadharVerfication,
          KycVerificationSteps.panVerification,
          KycVerificationSteps.annualTurnoverDeclaration,
          KycVerificationSteps.iecVerification,
          KycVerificationSteps.registeredOfficeAddress,
          KycVerificationSteps.bankAccountLinking,
        ];
      } else if (businessType == 'Sole Proprietorship') {
        steps = [
          KycVerificationSteps.aadharVerfication,
          KycVerificationSteps.panVerification,
          KycVerificationSteps.annualTurnoverDeclaration,
          KycVerificationSteps.iecVerification,
          KycVerificationSteps.registeredOfficeAddress,
          KycVerificationSteps.bankAccountLinking,
        ];
      } else {
        // Default case for other business types
        steps =
            KycVerificationSteps.values
                .where(
                  (step) =>
                      step != KycVerificationSteps.companyIncorporationVerification &&
                      step != KycVerificationSteps.contactInformation,
                )
                .toList();
      }

      print('Business Type: $businessType');
      print('Current Step: $currentStep');
      print('Available Steps: $steps');

      final currentIndex = steps.indexOf(currentStep);
      print('Current Index: $currentIndex');

      if (currentIndex >= 0 && currentIndex < steps.length - 1) {
        final nextStep = steps[currentIndex + 1];
        print('Next Step: $nextStep');
        return nextStep;
      }

      print('No next step available');
      return null;
    } catch (e) {
      Logger.error('Error getting next KYC step: $e');
      return null;
    }
  }

  /// Helper method to get the previous KYC step based on business type
  Future<KycVerificationSteps?> _getPreviousKycStep(KycVerificationSteps currentStep) async {
    try {
      final businessType = await _getBusinessType();

      List<KycVerificationSteps> steps;
      if (businessType == 'Company') {
        steps = [
          KycVerificationSteps.aadharVerfication,
          KycVerificationSteps.companyIncorporationVerification,
          KycVerificationSteps.annualTurnoverDeclaration,
          KycVerificationSteps.iecVerification,
          KycVerificationSteps.panVerification,
          KycVerificationSteps.contactInformation,
          KycVerificationSteps.registeredOfficeAddress,
          KycVerificationSteps.bankAccountLinking,
        ];
      } else if (businessType == 'LLP (Limited Liability Partnership)' || businessType == 'Partnership Firm') {
        steps = KycVerificationSteps.values.where((step) => step != KycVerificationSteps.contactInformation).toList();
      } else if (businessType == 'HUF (Hindu Undivided Family)') {
        steps = [
          KycVerificationSteps.aadharVerfication,
          KycVerificationSteps.panVerification,
          KycVerificationSteps.annualTurnoverDeclaration,
          KycVerificationSteps.iecVerification,
          KycVerificationSteps.registeredOfficeAddress,
          KycVerificationSteps.bankAccountLinking,
        ];
      } else if (businessType == 'Sole Proprietorship') {
        steps = [
          KycVerificationSteps.aadharVerfication,
          KycVerificationSteps.panVerification,
          KycVerificationSteps.annualTurnoverDeclaration,
          KycVerificationSteps.iecVerification,
          KycVerificationSteps.registeredOfficeAddress,
          KycVerificationSteps.bankAccountLinking,
        ];
      } else {
        // Default case for other business types
        steps =
            KycVerificationSteps.values
                .where(
                  (step) =>
                      step != KycVerificationSteps.companyIncorporationVerification &&
                      step != KycVerificationSteps.contactInformation,
                )
                .toList();
      }

      final currentIndex = steps.indexOf(currentStep);
      if (currentIndex > 0) {
        return steps[currentIndex - 1];
      }
      return null;
    } catch (e) {
      Logger.error('Error getting previous KYC step: $e');
      return null;
    }
  }

  /// Helper method to get all available steps for a business type
  Future<List<KycVerificationSteps>> _getAvailableSteps() async {
    try {
      final businessType = await _getBusinessType();

      if (businessType == 'Company') {
        return [
          KycVerificationSteps.aadharVerfication,
          KycVerificationSteps.companyIncorporationVerification,
          KycVerificationSteps.annualTurnoverDeclaration,
          KycVerificationSteps.iecVerification,
          KycVerificationSteps.panVerification,
          KycVerificationSteps.contactInformation,
          KycVerificationSteps.registeredOfficeAddress,
          KycVerificationSteps.bankAccountLinking,
        ];
      } else if (businessType == 'LLP (Limited Liability Partnership)' || businessType == 'Partnership Firm') {
        return KycVerificationSteps.values.where((step) => step != KycVerificationSteps.contactInformation).toList();
      } else if (businessType == 'HUF (Hindu Undivided Family)') {
        return [
          KycVerificationSteps.aadharVerfication,
          KycVerificationSteps.panVerification,
          KycVerificationSteps.annualTurnoverDeclaration,
          KycVerificationSteps.iecVerification,
          KycVerificationSteps.registeredOfficeAddress,
          KycVerificationSteps.bankAccountLinking,
        ];
      } else if (businessType == 'Sole Proprietorship') {
        return [
          KycVerificationSteps.aadharVerfication,
          KycVerificationSteps.panVerification,
          KycVerificationSteps.annualTurnoverDeclaration,
          KycVerificationSteps.iecVerification,
          KycVerificationSteps.registeredOfficeAddress,
          KycVerificationSteps.bankAccountLinking,
        ];
      } else {
        // Default case for other business types
        return KycVerificationSteps.values
            .where(
              (step) =>
                  step != KycVerificationSteps.companyIncorporationVerification &&
                  step != KycVerificationSteps.contactInformation,
            )
            .toList();
      }
    } catch (e) {
      Logger.error('Error getting available steps: $e');
      return [];
    }
  }

  void _onNavigateToNextKycStep(NavigateToNextKycStep event, Emitter<BusinessAccountSetupState> emit) async {
    final nextStep = await _getNextKycStep(state.currentKycVerificationStep);
    if (nextStep != null) {
      add(KycStepChanged(nextStep));
    }
  }

  void _onNavigateToPreviousKycStep(NavigateToPreviousKycStep event, Emitter<BusinessAccountSetupState> emit) async {
    final previousStep = await _getPreviousKycStep(state.currentKycVerificationStep);
    if (previousStep != null) {
      add(KycStepChanged(previousStep));
    }
    // If no previous step, the UI will handle navigation back
  }

  void _onGetAvailableKycSteps(GetAvailableKycSteps event, Emitter<BusinessAccountSetupState> emit) async {
    // This event can be used to get available steps if needed in the future
    // For now, we'll just log the available steps
    final availableSteps = await _getAvailableSteps();
    print('Available KYC Steps: $availableSteps');
  }

  Future<String?> _getBusinessType() async {
    final user = await Prefobj.preferences.get(Prefkeys.userDetail);
    final userDetail = jsonDecode(user!);
    final businessDetails = userDetail['business_details'];
    final businessType = businessDetails != null ? businessDetails['business_type'] : null;
    return businessType;
  }
}
