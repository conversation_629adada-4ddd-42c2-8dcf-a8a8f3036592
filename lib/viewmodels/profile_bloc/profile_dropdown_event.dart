import 'package:equatable/equatable.dart';

abstract class ProfileDropdownEvent extends Equatable {
  const ProfileDropdownEvent();

  @override
  List<Object> get props => [];
}

class ToggleDropdownEvent extends ProfileDropdownEvent {}

class CloseDropdownEvent extends ProfileDropdownEvent {}

class LogoutRequestedEvent extends ProfileDropdownEvent {}

class LogoutConfirmedEvent extends ProfileDropdownEvent {}

class LogoutWithEmailRequested extends ProfileDropdownEvent {
  final String email;
  const LogoutWithEmailRequested(this.email);

  @override
  List<Object> get props => [email];
}
