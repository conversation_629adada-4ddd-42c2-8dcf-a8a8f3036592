import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/widgets/account_setup_widgets/aadhar_upload_note.dart';
import 'package:exchek/widgets/account_setup_widgets/captcha_image.dart';
import 'package:exchek/widgets/common_widget/comman_verified_info_box.dart';

class AuthorizedDirectorKycDialog extends StatelessWidget {
  const AuthorizedDirectorKycDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(20),
          child: Container(
            clipBehavior: Clip.hardEdge,
            constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxDialogWidth(context)),
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.fillColor,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                buildSizedBoxH(25.0),
                _buildDialogHeader(context, state),
                buildSizedBoxH(10.0),
                divider(context),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: ResponsiveHelper.getMaxDialogWidth(context),
                    maxHeight:
                        MediaQuery.of(context).size.height < 600
                            ? MediaQuery.of(context).size.height * 0.52
                            : MediaQuery.of(context).size.height * 0.7,
                  ),
                  child: SingleChildScrollView(
                    padding: ResponsiveHelper.getScreenPadding(context),
                    child: _buildStepContent(context, state),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDialogHeader(BuildContext context, BusinessAccountSetupState state) {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxDialogWidth(context)),
        padding: ResponsiveHelper.getScreenPadding(context),
        child: Row(
          children: [
            Expanded(
              child: Text(
                "Authorized Director KYC",
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontSize: ResponsiveHelper.getFontSize(context, mobile: 20, tablet: 22, desktop: 24),
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.24,
                ),
              ),
            ),
            buildSizedboxW(15.0),
            CustomImageView(
              imagePath: Assets.images.svgs.icons.icClose.path,
              height: 50.0,
              onTap: () {
                GoRouter.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget divider(BuildContext context) =>
      Container(height: 1.5, width: double.maxFinite, color: Theme.of(context).customColors.lightBorderColor);

  Widget _buildStepContent(BuildContext context, BusinessAccountSetupState state) {
    switch (state.directorKycStep) {
      case DirectorKycSteps.panDetails:
        return _buildPanDetailsStep(context, state);
      case DirectorKycSteps.aadharDetails:
        return _buildAadharDetailsStep(context, state);
    }
  }

  Widget _buildPanDetailsStep(BuildContext context, BusinessAccountSetupState state) {
    return Form(
      key: state.directorsPanVerificationKey,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            buildSizedBoxH(30.0),
            _buildContentTitle(context, "PAN Details"),
            buildSizedBoxH(22.0),
            _buildDirector1PanNumberField(context, state),
            if (!state.isDirector1PanDetailsVerified) buildSizedBoxH(24.0),
            if (!state.isDirector1PanDetailsVerified) _buildVerifyDirector1PanButton(context, state),
            if (state.isDirector1PanDetailsVerified == true) ...[
              buildSizedBoxH(24.0),
              _buildDirector1PanName(context, state),
              buildSizedBoxH(24.0),
              _buildDirector1UploadPanCard(context, state),
              buildSizedBoxH(8.0),
              _buildDirector1IsBeneficialOwner(context, state),
              buildSizedBoxH(8.0),
              _buildDirector1BusinessRepresentative(context, state),
              buildSizedBoxH(30.0),
              _buildBusinessPanSaveButton(context, state),
            ],
            buildSizedBoxH(30.0),
          ],
        ),
      ),
    );
  }

  Widget _buildAadharDetailsStep(BuildContext context, BusinessAccountSetupState state) {
    if (state.isAadharVerified == false) {
      return _buildIsNotAadharVerify(context, state);
    } else {
      return _buildIsAadharVerify(context, state);
    }
  }

  Widget _buildBusinessPanSaveButton(BuildContext context, BusinessAccountSetupState state) {
    final isDisable = !(state.director1PanCardFile != null && state.director1PanNumberController.text.isNotEmpty);
    return Align(
      alignment: Alignment.centerRight,
      child: CustomElevatedButton(
        isShowTooltip: true,
        text: Lang.of(context).save_and_next,
        borderRadius: 8.0,
        width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 120 : double.maxFinite,
        buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 16.0,
          color: Theme.of(context).colorScheme.onPrimary,
        ),
        isLoading: state.isDirectorPanCardSaveLoading ?? false,
        isDisabled: isDisable,
        tooltipMessage: Lang.of(context).lbl_tooltip_text,
        onPressed:
            isDisable
                ? null
                : () {
                  if (state.directorsPanVerificationKey.currentState?.validate() ?? false) {
                    context.read<BusinessAccountSetupBloc>().add(
                      SaveDirectorPanDetails(
                        director1fileData: state.director1PanCardFile,
                        director1panName: state.director1PanNameController.text,
                        director1panNumber: state.director1PanNumberController.text,
                      ),
                    );
                  }
                },
      ),
    );
  }

  Widget _buildDirector1PanNumberField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_PAN_number,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          controller: state.director1PanNumberController,
          textInputAction: TextInputAction.done,
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
          validator: (value) {
            return ExchekValidations.validatePANByType(value, "INDIVIDUAL");
          },
          maxLength: 10,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          inputFormatters: [UpperCaseTextFormatter()],
          onChanged: (value) {
            context.read<BusinessAccountSetupBloc>().add(Director1PanNumberChanged(value));
          },
          onFieldSubmitted: (value) {
            if (ExchekValidations.validatePANByType(state.director1PanNumberController.text, "INDIVIDUAL") == null) {
              context.read<BusinessAccountSetupBloc>().add(
                GetDirector1PanDetails(state.director1PanNumberController.text),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildDirector1UploadPanCard(BuildContext context, BusinessAccountSetupState state) {
    return CustomFileUploadWidget(
      title: "Upload Director 1 PAN Card",
      selectedFile: state.director1PanCardFile,
      onFileSelected: (fileData) {
        context.read<BusinessAccountSetupBloc>().add(Director1UploadPanCard(fileData));
      },
    );
  }

  Widget _buildDirector1IsBeneficialOwner(BuildContext context, BusinessAccountSetupState state) {
    return CustomCheckBoxLabel(
      isSelected: state.director1BeneficialOwner,
      label: Lang.of(context).lbl_this_person_beneficial_owner,
      onChanged: () {
        context.read<BusinessAccountSetupBloc>().add(
          ChangeDirector1IsBeneficialOwner(isSelected: !state.director1BeneficialOwner),
        );
      },
    );
  }

  Widget _buildDirector1BusinessRepresentative(BuildContext context, BusinessAccountSetupState state) {
    return CustomCheckBoxLabel(
      isSelected: state.ditector1BusinessRepresentative,
      label: Lang.of(context).lbl_also_business_representative,
      onChanged: () {
        context.read<BusinessAccountSetupBloc>().add(
          ChangeDirector1IsBusinessRepresentative(isSelected: !state.ditector1BusinessRepresentative),
        );
      },
    );
  }

  Widget _buildContentTitle(BuildContext context, String title) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Row(
          children: [
            if (state.directorKycStep == DirectorKycSteps.aadharDetails) ...[
              CustomImageView(
                imagePath: Assets.images.svgs.icons.icArrowLeft.path,
                height: 24.0,
                onTap: () {
                  context.read<BusinessAccountSetupBloc>().add(DirectorKycStepChanged(DirectorKycSteps.panDetails));
                },
              ),
              buildSizedboxW(15.0),
            ],
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontSize: ResponsiveHelper.getFontSize(context, mobile: 18, tablet: 20, desktop: 20),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            buildSizedboxW(10.0),
            Text(
              "${_getCurrentStepNumber(state.directorKycStep)}/2",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 20, desktop: 20),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        );
      },
    );
  }

  int _getCurrentStepNumber(DirectorKycSteps step) {
    switch (step) {
      case DirectorKycSteps.panDetails:
        return 1;
      case DirectorKycSteps.aadharDetails:
        return 2;
    }
  }

  Widget _buildVerifyDirector1PanButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.director1PanNumberController]),
        builder: (context, _) {
          bool isDisabled =
              ExchekValidations.validatePANByType(state.director1PanNumberController.text, "INDIVIDUAL") != null;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 130 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isDirector1PanDetailsLoading,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisabled,
            borderRadius: 8.0,
            onPressed: () {
              if (ExchekValidations.validatePANByType(state.director1PanNumberController.text, "INDIVIDUAL") == null) {
                context.read<BusinessAccountSetupBloc>().add(
                  GetDirector1PanDetails(state.director1PanNumberController.text),
                );
              }
            },
          );
        },
      ),
    );
  }

  Widget _buildDirector1PanName(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_name_on_PAN,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CommanVerifiedInfoBox(value: state.fullDirector1NamePan ?? '', showTrailingIcon: true),
      ],
    );
  }

  Widget _buildIsAadharVerify(BuildContext context, BusinessAccountSetupState state) {
    return Form(
      key: state.aadharVerificationFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildSizedBoxH(30.0),
          _buildContentTitle(context, "Aadhar Details "),
          buildSizedBoxH(22.0),
          _buildVerifyAadharNumber(context, state),
          buildSizedBoxH(24.0),
          UploadNote(notes: [Lang.of(context).lbl_note_1, Lang.of(context).lbl_note_2]),
          buildSizedBoxH(24.0),
          _buildUploadAddharCard(context),
          buildSizedBoxH(30.0),
          _buildNextButton(),
          buildSizedBoxH(30.0),
        ],
      ),
    );
  }

  Widget _buildIsNotAadharVerify(BuildContext context, BusinessAccountSetupState state) {
    return Form(
      key: state.aadharVerificationFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildSizedBoxH(30.0),
          _buildContentTitle(context, "Aadhar Details"),
          buildSizedBoxH(22.0),
          _buildAadharNumberField(context, state),
          if (!state.isDirectorCaptchaSend && !state.isOtpSent) buildSizedBoxH(20),
          if (!state.isDirectorCaptchaSend && !state.isOtpSent) _buildVerifyAadharButton(context, state),
          if (state.isDirectorCaptchaSend) ...[
            buildSizedBoxH(24.0),
            Builder(
              builder: (context) {
                if (state.directorCaptchaImage != null) {
                  return Column(
                    children: [
                      Row(
                        spacing: 20.0,
                        children: [
                          Base64CaptchaField(base64Image: state.directorCaptchaImage ?? ''),
                          AbsorbPointer(
                            absorbing: state.isOtpSent,
                            child: Opacity(
                              opacity: state.isOtpSent ? 0.5 : 1.0,
                              child: CustomImageView(
                                imagePath: Assets.images.svgs.icons.icRefresh.path,
                                height: 40.0,
                                width: 40.0,
                                onTap: () async {
                                  context.read<BusinessAccountSetupBloc>().add(DirectorReCaptchaSend());
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      buildSizedBoxH(24.0),
                      _buildCaptchaField(context),
                      if (!state.isOtpSent) ...[buildSizedBoxH(24.0), _buildSendOTPButton(context, state)],
                    ],
                  );
                } else {
                  return const SizedBox.shrink();
                }
              },
            ),
          ],
          if (state.isOtpSent) ...[
            buildSizedBoxH(24.0),
            _buildOTPField(context, state),
            if (state.isAadharOTPInvalidate.isNotEmpty) ...[
              buildSizedBoxH(10),
              Text(
                state.isAadharOTPInvalidate,
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xffFF5D5F),
                ),
              ),
            ],
            buildSizedBoxH(30.0),
            _buildVerifyButton(context, state),
          ],
          buildSizedBoxH(30.0),
        ],
      ),
    );
  }

  Widget _buildAadharNumberField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_aadhar_number,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CustomTextInputField(
          context: context,
          type: InputType.digits,
          controller: state.aadharNumberController,
          maxLength: 14,
          textInputAction: TextInputAction.done,
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            GroupedInputFormatter(groupSizes: [4, 4, 4], separator: '-', digitsOnly: false, toUpperCase: true),
          ],
          validator: ExchekValidations.validateAadhaar,
          onChanged: (value) {
            context.read<BusinessAccountSetupBloc>().add(DirectorAadharNumberChanged(value));
          },
          onFieldSubmitted: (value) {
            final isValidAadhar = state.aadharNumberController.text.trim().length == 14;
            if (isValidAadhar) {
              context.read<BusinessAccountSetupBloc>().add(DirectorCaptchaSend());
            }
          },
        ),
      ],
    );
  }

  Widget _buildSendOTPButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.aadharNumberController]),
        builder: (context, child) {
          final isValidAadhar = state.aadharNumberController.text.trim().length == 14;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_request_OTP,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isLoading: state.isDirectorAadharOtpLoading,
            isDisabled: !isValidAadhar,
            borderRadius: 8.0,
            onPressed:
                isValidAadhar
                    ? () async {
                      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                      context.read<BusinessAccountSetupBloc>().add(
                        SendAadharOtp(
                          state.aadharNumberController.text.trim(),
                          state.directorCaptchaInputController.text.trim(),
                          sessionId,
                        ),
                      );
                    }
                    : null,
          );
        },
      ),
    );
  }

  Widget _buildOTPField(BuildContext context, BusinessAccountSetupState state) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Lang.of(context).lbl_one_time_password,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
                fontWeight: FontWeight.w400,
                height: 1.22,
              ),
            ),
            buildSizedBoxH(8.0),
            CustomTextInputField(
              context: context,
              type: InputType.digits,
              controller: state.aadharOtpController,
              textInputAction: TextInputAction.done,
              validator: ExchekValidations.validateOTP,
              suffixText: true,
              suffixIcon: ValueListenableBuilder(
                valueListenable: state.aadharNumberController,
                builder: (context, _, __) {
                  return GestureDetector(
                    onTap:
                        state.isAadharOtpTimerRunning
                            ? null
                            : () async {
                              FocusManager.instance.primaryFocus?.unfocus();
                              final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                              BlocProvider.of<BusinessAccountSetupBloc>(context).add(
                                SendAadharOtp(
                                  state.aadharNumberController.text.trim(),
                                  state.directorCaptchaInputController.text.trim(),
                                  sessionId,
                                ),
                              );
                            },
                    child: Container(
                      color: Colors.transparent,
                      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
                      child: Text(
                        state.isAadharOtpTimerRunning
                            ? '${Lang.of(context).lbl_resend_otp_in} ${formatSecondsToMMSS(state.aadharOtpRemainingTime)}sec'
                            : Lang.of(context).lbl_resend_OTP,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color:
                              state.isAadharOtpTimerRunning || (state.aadharNumberController.text.trim().length != 14)
                                  ? Theme.of(context).customColors.textdarkcolor?.withValues(alpha: 0.5)
                                  : Theme.of(context).customColors.greenColor,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              ),
              maxLength: 6,
              contentPadding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
              inputFormatters: [FilteringTextInputFormatter.digitsOnly, NoPasteFormatter()],
              onFieldSubmitted: (value) {
                if (state.aadharVerificationFormKey.currentState?.validate() ?? false) {
                  context.read<BusinessAccountSetupBloc>().add(
                    AadharNumbeVerified(state.aadharNumberController.text, state.aadharOtpController.text),
                  );
                }
              },
              contextMenuBuilder: (BuildContext context, EditableTextState editableTextState) {
                return AdaptiveTextSelectionToolbar.buttonItems(
                  anchors: editableTextState.contextMenuAnchors,
                  buttonItems:
                      editableTextState.contextMenuButtonItems
                          .where((item) => item.type != ContextMenuButtonType.paste)
                          .toList(),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildVerifyButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.aadharOtpController, state.aadharNumberController]),
        builder: (context, child) {
          final isDisable = state.aadharOtpController.text.isEmpty || state.aadharOtpController.text.isEmpty;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_confirm_and_next,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isAadharVerifiedLoading ?? false,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisable,
            borderRadius: 8.0,
            onPressed:
                isDisable
                    ? null
                    : () {
                      if (state.aadharVerificationFormKey.currentState?.validate() ?? false) {
                        context.read<BusinessAccountSetupBloc>().add(
                          AadharNumbeVerified(state.aadharNumberController.text, state.aadharOtpController.text),
                        );
                      }
                    },
          );
        },
      ),
    );
  }

  Widget _buildVerifyAadharNumber(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_aadhar_number_verified,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
          ),
        ),
        buildSizedBoxH(8.0),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 14.0, vertical: 14.0),
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.fillColor,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(color: Theme.of(context).customColors.greenColor!),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  ((state.aadharNumber ?? "").contains("-"))
                      ? (state.aadharNumber ?? '')
                      : GroupedInputFormatter.format(
                        input: state.aadharNumber ?? '',
                        groupSizes: [4, 4, 4],
                        separator: '-',
                        digitsOnly: true,
                      ),
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 14, desktop: 14),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              CustomImageView(imagePath: Assets.images.svgs.icons.icShieldTick.path, height: 20.0),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUploadAddharCard(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomFileUploadWidget(
              selectedFile: state.frontSideAdharFile,
              title: "Upload Front Side of Aadhar Card",
              onFileSelected: (fileData) {
                context.read<BusinessAccountSetupBloc>().add(FrontSlideAadharCardUpload(fileData));
              },
            ),
            buildSizedBoxH(24.0),
            CustomFileUploadWidget(
              selectedFile: state.backSideAdharFile,
              title: "Upload Back Side of Aadhar Card",
              onFileSelected: (fileData) {
                context.read<BusinessAccountSetupBloc>().add(BackSlideAadharCardUpload(fileData));
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildNextButton() {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        final isButtonEnabled = state.frontSideAdharFile != null && state.backSideAdharFile != null;

        return Align(
          alignment: Alignment.centerRight,
          child: CustomElevatedButton(
            isShowTooltip: true,
            text: "Save",
            borderRadius: 8.0,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 120 : double.maxFinite,
            buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 16.0,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            isDisabled: !isButtonEnabled,
            isLoading: state.isAadharFileUploading,
            onPressed:
                isButtonEnabled
                    ? () {
                      context.read<BusinessAccountSetupBloc>().add(
                        AadharFileUploadSubmitted(
                          frontAadharFileData: state.frontSideAdharFile!,
                          backAadharFileData: state.backSideAdharFile!,
                        ),
                      );
                      // Close dialog after completion
                      GoRouter.of(context).pop();
                    }
                    : null,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
          ),
        );
      },
    );
  }

  String formatSecondsToMMSS(int seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final secs = (seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$secs';
  }

  Widget _buildCaptchaField(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Enter Captcha",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
                    fontWeight: FontWeight.w400,
                    height: 1.22,
                  ),
                ),
                buildSizedBoxH(8.0),
                AbsorbPointer(
                  absorbing: state.isOtpSent && state.directorCaptchaInputController.text.isNotEmpty,
                  child: CustomTextInputField(
                    context: context,
                    type: InputType.text,
                    controller: state.directorCaptchaInputController,
                    textInputAction: TextInputAction.done,
                    contentPadding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
                    onFieldSubmitted:
                        state.isDirectorCaptchaLoading == true
                            ? null
                            : (value) async {
                              final isCaptchaValid = state.directorCaptchaInputController.text.isNotEmpty;

                              if (isCaptchaValid) {
                                final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                                context.read<BusinessAccountSetupBloc>().add(
                                  SendAadharOtp(
                                    state.aadharNumberController.text.trim(),
                                    state.directorCaptchaInputController.text.trim(),
                                    sessionId,
                                  ),
                                );
                              }
                            },
                    validator: ExchekValidations.validateRequired,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildVerifyAadharButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.aadharNumberController]),
        builder: (context, _) {
          bool isDisabled =
              ExchekValidations.validateAadhaar(state.aadharNumberController.text.replaceAll("-", "").trim()) != null;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isDirectorCaptchaLoading ?? false,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisabled,
            borderRadius: 8.0,
            onPressed:
                isDisabled
                    ? null
                    : () {
                      final isValidAadhar =
                          ExchekValidations.validateAadhaar(
                            state.aadharNumberController.text.replaceAll("-", "").trim(),
                          ) ==
                          null;
                      if (isValidAadhar) {
                        context.read<BusinessAccountSetupBloc>().add(DirectorCaptchaSend());
                      }
                    },
          );
        },
      ),
    );
  }
}
