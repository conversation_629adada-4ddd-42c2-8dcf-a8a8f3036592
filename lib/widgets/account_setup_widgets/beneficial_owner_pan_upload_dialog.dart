import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/widgets/common_widget/comman_verified_info_box.dart';

class BeneficialOwnerPanUploadDialog extends StatelessWidget {
  const BeneficialOwnerPanUploadDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(20),
          child: Container(
            clipBehavior: Clip.hardEdge,
            constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxDialogWidth(context)),
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.fillColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                buildSizedBoxH(25.0),
                _buildDialogHeader(context),
                buildSizedBoxH(10.0),
                divider(context),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: ResponsiveHelper.getMaxDialogWidth(context),
                    maxHeight:
                        MediaQuery.of(context).size.height < 600
                            ? MediaQuery.of(context).size.height * 0.52
                            : MediaQuery.of(context).size.height * 0.7,
                  ),
                  child: SingleChildScrollView(
                    padding: ResponsiveHelper.getScreenPadding(context),
                    child: Form(
                      key: state.beneficialOwnerPanVerificationKey,
                      child: Center(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            buildSizedBoxH(20.0),
                            _buildBeneficialPanNumberField(context, state),
                            if (!state.isBeneficialOwnerPanDetailsVerified) buildSizedBoxH(24.0),
                            if (!state.isBeneficialOwnerPanDetailsVerified) _buildVerifyPanButton(context, state),
                            buildSizedBoxH(24.0),
                            if (state.isBeneficialOwnerPanDetailsVerified == true) ...[
                              _buildBaneficialOwnerPanNameField(context, state),
                              buildSizedBoxH(24.0),
                              _buildBeneficialUploadPanCard(context, state),
                              buildSizedBoxH(44.0),
                              _buildBusinessPanSaveButton(),
                              buildSizedBoxH(ResponsiveHelper.isWebAndIsNotMobile(context) ? 60.0 : 20.0),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDialogHeader(BuildContext context) {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxDialogWidth(context)),
        padding: ResponsiveHelper.getScreenPadding(context),
        child: Row(
          children: [
            Expanded(
              child: Text(
                Lang.of(context).lbl_beneficial_owner_PAN_details,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontSize: ResponsiveHelper.getFontSize(context, mobile: 20, tablet: 22, desktop: 24),
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.24,
                ),
              ),
            ),
            buildSizedboxW(15.0),
            CustomImageView(
              imagePath: Assets.images.svgs.icons.icClose.path,
              height: 50.0,
              onTap: () {
                GoRouter.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget divider(BuildContext context) =>
      Container(height: 1.5, width: double.maxFinite, color: Theme.of(context).customColors.borderColor);

  Widget _buildBeneficialPanNumberField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_PAN_number,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          controller: state.beneficialOwnerPanNumberController,
          textInputAction: TextInputAction.done,
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
          validator: (value) {
            return ExchekValidations.validatePANByType(value, "INDIVIDUAL");
          },
          maxLength: 10,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          inputFormatters: [UpperCaseTextFormatter()],
          onChanged: (value) {
            context.read<BusinessAccountSetupBloc>().add(BeneficialOwnerPanNumberChanged(value));
          },
          onFieldSubmitted: (value) {
            state.beneficialOwnerPanVerificationKey.currentState?.validate();
            if (ExchekValidations.validatePANByType(state.beneficialOwnerPanNumberController.text, "INDIVIDUAL") ==
                null) {
              context.read<BusinessAccountSetupBloc>().add(
                GetBeneficialOwnerPanDetails(state.beneficialOwnerPanNumberController.text),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildBeneficialUploadPanCard(BuildContext context, BusinessAccountSetupState state) {
    return CustomFileUploadWidget(
      title: "Upload Beneficial Owner PAN Card",
      selectedFile: state.beneficialOwnerPanCardFile,
      onFileSelected: (fileData) {
        context.read<BusinessAccountSetupBloc>().add(BeneficialOwnerUploadPanCard(fileData));
      },
    );
  }

  // Widget _buildBeneficialOwnerIsDirector(BuildContext context, BusinessAccountSetupState state) {
  //   return CustomCheckBoxLabel(
  //     isSelected: state.beneficialOwnerIsDirector,
  //     label: Lang.of(context).lbl_director_person,
  //     onChanged: () {
  //       context.read<BusinessAccountSetupBloc>().add(
  //         ChangeBeneficialOwnerIsDirector(isSelected: !state.beneficialOwnerIsDirector),
  //       );
  //     },
  //   );
  // }

  // Widget _buildBeneficialOwnerBusinessRepresentative(BuildContext context, BusinessAccountSetupState state) {
  //   return CustomCheckBoxLabel(
  //     isSelected: state.benificialOwnerBusinessRepresentative,
  //     label: Lang.of(context).lbl_business_Representative,
  //     onChanged: () {
  //       context.read<BusinessAccountSetupBloc>().add(
  //         ChangeBeneficialOwnerIsBusinessRepresentative(isSelected: !state.benificialOwnerBusinessRepresentative),
  //       );
  //     },
  //   );
  // }

  Widget _buildBusinessPanSaveButton() {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        final isDisable =
            !(state.beneficialOwnerPanCardFile != null && state.beneficialOwnerPanNumberController.text.isNotEmpty);
        return Align(
          alignment: Alignment.centerRight,
          child: CustomElevatedButton(
            isShowTooltip: true,
            text: Lang.of(context).lbl_save,
            borderRadius: 8.0,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 125 : double.maxFinite,
            buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 16.0,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            isLoading: state.isBeneficialOwnerPanCardSaveLoading ?? false,
            isDisabled: isDisable,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            onPressed:
                isDisable
                    ? null
                    : () {
                      if (state.beneficialOwnerPanVerificationKey.currentState?.validate() ?? false) {
                        context.read<BusinessAccountSetupBloc>().add(
                          SaveBeneficialOwnerPanDetails(
                            fileData: state.beneficialOwnerPanCardFile,
                            panName: state.beneficialOwnerPanNameController.text,
                            panNumber: state.beneficialOwnerPanNumberController.text,
                          ),
                        );
                      }
                    },
          ),
        );
      },
    );
  }

  Widget _buildVerifyPanButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.beneficialOwnerPanNumberController]),
        builder: (context, _) {
          bool isDisabled =
              ExchekValidations.validatePANByType(state.beneficialOwnerPanNumberController.text, "INDIVIDUAL") != null;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 125 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isBeneficialOwnerPanDetailsLoading,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisabled,
            borderRadius: 8.0,
            onPressed: () {
              // Show validation error if any
              state.beneficialOwnerPanVerificationKey.currentState?.validate();
              if (ExchekValidations.validatePANByType(state.beneficialOwnerPanNumberController.text, "INDIVIDUAL") ==
                  null) {
                context.read<BusinessAccountSetupBloc>().add(
                  GetBeneficialOwnerPanDetails(state.beneficialOwnerPanNumberController.text),
                );
              }
            },
          );
        },
      ),
    );
  }

  Widget _buildBaneficialOwnerPanNameField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_name_on_PAN,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CommanVerifiedInfoBox(value: state.fullBeneficialOwnerNamePan ?? '', showTrailingIcon: true),
      ],
    );
  }
}
