import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/viewmodels/profile_bloc/profile_dropdown_bloc.dart';
import 'package:exchek/viewmodels/profile_bloc/profile_dropdown_state.dart';
import 'package:exchek/widgets/common_widget/app_toast_message.dart';
import 'package:exchek/viewmodels/profile_bloc/profile_dropdown_event.dart';

class ProfileDropdown extends StatelessWidget {
  final String userName;
  final bool isBusinessUser;
  final String email;
  final VoidCallback? onManageAccount;
  final VoidCallback? onChangePassword;
  final VoidCallback? onLogout;

  const ProfileDropdown({
    super.key,
    required this.userName,
    required this.isBusinessUser,
    required this.email,
    this.onManageAccount,
    this.onChangePassword,
    this.onLogout,
  });

  @override
  Widget build(BuildContext context) {
    final authState = context.watch<AuthBloc>().state;
    String userName = authState.userName ?? '';
    String email = authState.email ?? '';
    String phoneNumber = authState.phoneNumber ?? '';

    return FutureBuilder<List<String>>(
      future: () async {
        if (userName.isEmpty) {
          userName = await Prefobj.preferences.get(Prefkeys.loggedUserName) ?? '';
        }
        if (email.isEmpty) {
          email = await Prefobj.preferences.get(Prefkeys.loggedEmail) ?? '';
        }
        if (phoneNumber.isEmpty) {
          phoneNumber = await Prefobj.preferences.get(Prefkeys.loggedPhoneNumber) ?? '';
        }
        return [userName, email, phoneNumber];
      }(),
      builder: (context, snapshot) {
        final userNameVal = snapshot.data?[0] ?? '';
        final emailVal = snapshot.data?[1] ?? '';
        final truncatedUserName = userNameVal.length > 12 ? '${userNameVal.substring(0, 10)}...' : userNameVal;
        return BlocProvider(
          create: (_) => ProfileDropdownBloc(),
          child: BlocListener<ProfileDropdownBloc, ProfileDropdownState>(
            listener: (context, state) {
              if (state is ProfileDropdownLogoutSuccess) {
                Prefobj.preferences.delete(Prefkeys.authToken).then((_) {
                  context.read<AuthBloc>().add(ClearLoginDataManuallyEvent());
                  GoRouter.of(context).go(RouteUri.loginRoute);
                });
              } else if (state is ProfileDropdownLogoutFailure) {
                AppToast.show(message: state.message, type: ToastificationType.error);
              }
            },
            child: BlocBuilder<ProfileDropdownBloc, ProfileDropdownState>(
              builder: (context, state) {
                if (state is ProfileDropdownLoggingOut) {
                  // return const Center(child: CircularProgressIndicator());
                }
                return PopupMenuButton<String>(
                  offset: const Offset(0, 90),
                  padding: EdgeInsets.zero,
                  menuPadding: EdgeInsets.zero,
                  elevation: 8,
                  color: Colors.grey.shade50,
                  shadowColor: Theme.of(context).customColors.shadowColor!.withValues(alpha: 0.25),
                  clipBehavior: Clip.antiAlias,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  child: Container(
                    padding:
                        ResponsiveHelper.isWebAndIsNotMobile(context)
                            ? const EdgeInsets.symmetric(horizontal: 12, vertical: 10)
                            : const EdgeInsets.symmetric(horizontal: 20, vertical: 2),
                    decoration: BoxDecoration(color: Theme.of(context).customColors.fillColor),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircleAvatar(
                          radius: 20,
                          backgroundColor: Theme.of(context).customColors.primaryColor,
                          child: Text(
                            getInitials(userNameVal),
                            style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
                          ),
                        ),
                        // buildSizedboxW(20.0),
                        // if (ResponsiveHelper.isWebAndIsNotMobile(context))
                        //   Tooltip(
                        //     message: userNameVal,
                        //     child: Text(
                        //       truncatedUserName,
                        //       style: TextStyle(
                        //         fontSize: 14,
                        //         fontWeight: FontWeight.w200,
                        //         color: Theme.of(context).customColors.darktextcolor,
                        //       ),
                        //     ),
                        //   ),
                        // buildSizedboxW(4.0),
                        // if (ResponsiveHelper.isWebAndIsNotMobile(context))
                        //   Icon(
                        //     Icons.keyboard_arrow_down,
                        //     size: 18,
                        //     color: Theme.of(context).customColors.darktextcolor,
                        //   ),
                      ],
                    ),
                  ),
                  onSelected: (value) {
                    switch (value) {
                      case 'view_profile_header':
                      case 'view_profile':
                        // _showProfileDialog(
                        //   context,
                        //   userNameVal,
                        //   emailVal,
                        //   phoneNumber,
                        // );
                        break;
                      case 'manage_account':
                        onManageAccount?.call();
                        break;
                      case 'change_password':
                        // _showChangePasswordDialog(context);
                        break;
                      case 'logout':
                        _showLogoutConfirmationDialog(context, emailVal);
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        PopupMenuItem<String>(
                          padding: EdgeInsets.zero,
                          value: 'view_profile_header',
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).customColors.fillColor,
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context).customColors.darkShadowColor!.withValues(alpha: 0.03),
                                  blurRadius: 10.0,
                                ),
                              ],
                            ),
                            // padding: EdgeInsets.all(16),
                            child: Row(
                              children: [
                                // CircleAvatar(
                                //   radius: 20,
                                //   backgroundColor: Theme.of(context).customColors.primaryColor,
                                //   child: Text(
                                //     getInitials(userNameVal),
                                //     style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
                                //   ),
                                // ),
                                // buildSizedboxW(12.0),
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        userNameVal,
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                          color: Theme.of(context).customColors.blackColor,
                                        ),
                                      ),
                                      Text(
                                        emailVal,
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: Theme.of(context).customColors.darktextcolor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // PopupMenuItem<String>(
                        //   enabled: false,
                        //   height: 0,
                        //   child: Divider(
                        //     thickness: 0.5,
                        //     height: 1,
                        //     color: Theme.of(context).customColors.darktextcolor,
                        //   ),
                        // ),

                        // Profile menu item - show if eKYC is completed
                        // if (authState.isEkycCompleted)
                        // PopupMenuItem<String>(
                        //   value: 'view_profile',
                        //   child: _buildMenuItem(
                        //     context: context,
                        //     iconWidget: CustomImageView(
                        //       imagePath:
                        //           Assets
                        //               .images
                        //               .svgs
                        //               .icons
                        //               .profileManagement
                        //               .path,
                        //       height: 18.0,
                        //     ),
                        //     text: Lang.of(context).profile_manageAccount,
                        //   ),
                        // ),
                        // if (authState.isEkycCompleted)
                        // PopupMenuItem<String>(
                        //   enabled: false,
                        //   height: 0,
                        //   child: Divider(
                        //     thickness: 0.5,
                        //     height: 1,
                        //     color: Theme.of(context).customColors.darktextcolor,
                        //   ),
                        // ),
                        // PopupMenuItem<String>(
                        //   value: 'change_password',
                        //   padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                        //   child: _buildMenuItem(
                        //     context: context,
                        //     iconWidget: CustomImageView(
                        //       imagePath: Assets.images.svgs.icons.profileChangePassword.path,
                        //       height: 18.0,
                        //     ),
                        //     text: Lang.of(context).profile_changePassword,
                        //   ),
                        // ),
                        // PopupMenuItem<String>(
                        //   padding: EdgeInsets.zero,
                        //   enabled: false,
                        //   height: 0,
                        //   child: Divider(
                        //     thickness: 0.5,
                        //     height: 1,
                        //     color: Theme.of(context).customColors.darktextcolor,
                        //   ),
                        // ),
                        PopupMenuItem<String>(
                          value: 'logout',
                          padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                          child: _buildMenuItem(
                            context: context,
                            iconWidget: CustomImageView(
                              imagePath: Assets.images.svgs.icons.profileLogout.path,
                              height: 18.0,
                            ),
                            text: 'Log out',
                          ),
                        ),
                      ],
                );
              },
            ),
          ),
        );
      },
    );
  }

  // / -------------------- Profile Dialog --------------------
  void _showProfileDialog(BuildContext context, String userName, String email, String phoneNumber) async {
    // Get additional profile data from storage
    // ignore: avoid_print

    // final phoneNumber =
    //     await Prefobj.preferences.get('loggedUserPhoneNumber') ??
    //     'Not provided';
    final accountType = await Prefobj.preferences.get('accountType') ?? 'Individual';
    // final registrationDate =
    //     await Prefobj.preferences.get('registrationDate') ?? 'Not available';
    final kycStatus = await Prefobj.preferences.get('kycStatus') ?? 'Pending';

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: Theme.of(context).customColors.shadowColor!.withOpacity(0.1),
                child: Text(
                  getInitials(userName),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).customColors.blackColor,
                    fontSize: 16,
                  ),
                ),
              ),
              SizedBox(width: 12),
              Text(
                'Profile Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).customColors.blackColor,
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProfileRow(context: context, icon: Icons.person_outline, label: 'Full Name', value: userName),
                SizedBox(height: 16),
                _buildProfileRow(context: context, icon: Icons.email_outlined, label: 'Email', value: email),
                SizedBox(height: 16),
                _buildProfileRow(
                  context: context,
                  icon: Icons.phone_outlined,
                  label: 'Phone Number',
                  value: phoneNumber,
                ),
                SizedBox(height: 16),
                _buildProfileRow(
                  context: context,
                  icon: Icons.business_outlined,
                  label: 'Account Type',
                  value: accountType,
                ),
                SizedBox(height: 16),
                // _buildProfileRow(
                //   context: context,
                //   icon: Icons.calendar_today_outlined,
                //   label: 'Registration Date',
                //   value: registrationDate,
                // ),
                SizedBox(height: 16),
                _buildProfileRow(
                  context: context,
                  icon: Icons.verified_user_outlined,
                  label: 'KYC Status',
                  value: kycStatus,
                  valueColor:
                      kycStatus.toLowerCase() == 'completed'
                          ? Theme.of(context).customColors.greenColor
                          : Theme.of(context).customColors.redColor,
                ),
              ],
            ),
          ),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text('Close', style: TextStyle(color: Theme.of(context).customColors.blackColor)),
            ),
            // if (authState.isEkycCompleted)
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                onManageAccount?.call();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).customColors.greenColor,
                foregroundColor: Theme.of(context).customColors.fillColor,
              ),
              child: const Text('Edit Profile'),
            ),
          ],
        );
      },
    );
  }

  /// -------------------- Profile Row Widget --------------------
  Widget _buildProfileRow({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Theme.of(context).customColors.darktextcolor),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Theme.of(context).customColors.darktextcolor,
                ),
              ),
              SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: valueColor ?? Theme.of(context).customColors.blackColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// -------------------- Logout Confirmation Dialog --------------------
  void _showLogoutConfirmationDialog(BuildContext context, String email) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(Lang.of(context).profile_confirmLogout),
          content: Text(Lang.of(context).profile_logoutConfirmationMessage),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          actions: [
            TextButton(onPressed: () => Navigator.of(dialogContext).pop(), child: const Text('Cancel')),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                context.read<ProfileDropdownBloc>().add(LogoutWithEmailRequested(email));
              },
              child: Text('Logout', style: TextStyle(color: Theme.of(context).customColors.redColor)),
            ),
            // ElevatedButton(
            //   onPressed: () {
            //     Navigator.of(dialogContext).pop();
            //     context.read<ProfileDropdownBloc>().add(LogoutWithEmailRequested(email));
            //   },
            //   style: ElevatedButton.styleFrom(
            //     backgroundColor: Theme.of(context).customColors.redColor,
            //     foregroundColor: Theme.of(context).customColors.fillColor,
            //   ),
            //   child: const Text('Logout'),
            // ),
          ],
        );
      },
    );
  }

  /// -------------------- Change Password Dialog --------------------
  // void _showChangePasswordDialog(BuildContext context) {
  //   final oldPasswordController = TextEditingController();
  //   final newPasswordController = TextEditingController();
  //   final formKey = GlobalKey<FormState>();
  //   showDialog(
  //     context: context,
  //     builder: (BuildContext dialogContext) {
  //       return AlertDialog(
  //         title: Text(Lang.of(context).profile_changePassword),
  //         content: Form(
  //           key: formKey,
  //           child: Column(
  //             mainAxisSize: MainAxisSize.min,
  //             children: [
  //               TextFormField(
  //                 controller: oldPasswordController,
  //                 obscureText: true,
  //                 decoration: InputDecoration(labelText: 'Old Password'),
  //                 validator:
  //                     (value) =>
  //                         value == null || value.isEmpty
  //                             ? 'Enter old password'
  //                             : null,
  //               ),
  //               SizedBox(height: 16),
  //               TextFormField(
  //                 controller: newPasswordController,
  //                 obscureText: true,
  //                 decoration: InputDecoration(labelText: 'New Password'),
  //                 validator:
  //                     (value) =>
  //                         value == null || value.isEmpty
  //                             ? 'Enter new password'
  //                             : null,
  //               ),
  //             ],
  //           ),
  //         ),
  //         shape: RoundedRectangleBorder(
  //           borderRadius: BorderRadius.circular(12),
  //         ),
  //         actions: [
  //           TextButton(
  //             onPressed: () => Navigator.of(dialogContext).pop(),
  //             child: const Text('Cancel'),
  //           ),
  //           ElevatedButton(
  //             onPressed: () {
  //               if (formKey.currentState?.validate() ?? false) {
  //                 // TODO: Implement password change logic here
  //                 Navigator.of(dialogContext).pop();
  //                 AppToast.show(
  //                   message: 'Password changed successfully',
  //                   type: ToastificationType.success,
  //                 );
  //               }
  //             },
  //             style: ElevatedButton.styleFrom(
  //               backgroundColor: Theme.of(context).customColors.greenColor,
  //               foregroundColor: Theme.of(context).customColors.fillColor,
  //             ),
  //             child: const Text('Submit'),
  //           ),
  //         ],
  //       );
  //     },
  //   );
  // }

  /// -------------------- Menu Item Widget --------------------
  Widget _buildMenuItem({required BuildContext context, required Widget iconWidget, String? text, Widget? child}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              // color: Colors.green.withOpacity(0.1),
              // borderRadius: BorderRadius.circular(6),
            ),
            child: Center(child: iconWidget),
          ),
          buildSizedboxW(10.0),
          child ??
              Text(
                text ?? '',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).customColors.blackColor,
                ),
              ),
        ],
      ),
    );
  }
}
