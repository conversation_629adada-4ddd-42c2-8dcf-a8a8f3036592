import 'package:exchek/core/utils/exports.dart';

class CustomTextInputField extends StatefulWidget {
  final BuildContext context;
  final TextEditingController? controller;
  final String? hintLabel;
  final String? label;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool? suffixText;
  final bool? filled;
  final Color? fillColor;
  final EdgeInsetsGeometry? contentPadding;
  final InputType type;
  final TextStyle? hintStyle;
  final TextInputAction? textInputAction;
  final BoxConstraints? boxConstraints;
  final BoxConstraints? suffixBoxConstraints;
  final String? Function(String?)? validator;
  final bool? obscuredText;
  final String? obscuringCharacter;
  final int? maxLength;
  final Function(String)? onChanged;
  final Function(String)? onFieldSubmitted;
  final InputDecoration? inputDecoration;
  final bool? isDense;
  final List<TextInputFormatter>? inputFormatters;
  final double? height;
  final EditableTextContextMenuBuilder? contextMenuBuilder;
  final AutovalidateMode? autovalidateMode;
  final double? textStyleFontSize;

  const CustomTextInputField({
    super.key,
    required this.context,
    this.controller,
    this.hintLabel,
    this.label,
    this.prefixIcon,
    this.suffixIcon,
    this.suffixText = false,
    this.filled,
    this.fillColor,
    this.contentPadding,
    required this.type,
    this.hintStyle,
    this.textInputAction,
    this.boxConstraints,
    this.validator,
    this.obscuredText,
    this.obscuringCharacter,
    this.maxLength,
    this.onChanged,
    this.onFieldSubmitted,
    this.inputDecoration,
    this.isDense = false,
    this.inputFormatters,
    this.suffixBoxConstraints,
    this.height,
    this.contextMenuBuilder,
    this.autovalidateMode,
    this.textStyleFontSize,
  });

  @override
  State<CustomTextInputField> createState() => _CustomTextInputFieldState();
}

class _CustomTextInputFieldState extends State<CustomTextInputField> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: TextFormField(
        clipBehavior: Clip.antiAlias,
        controller: widget.controller,
        keyboardType: _getKeyboardType(),
        textInputAction: widget.textInputAction ?? TextInputAction.next,
        validator: widget.validator,
        textAlignVertical: TextAlignVertical.center,
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontSize:
              widget.textStyleFontSize ?? ResponsiveHelper.getFontSize(context, mobile: 16, tablet: 16, desktop: 16),
          fontWeight: FontWeight.w400,
        ),
        obscureText: widget.obscuredText ?? false,
        obscuringCharacter: widget.obscuringCharacter ?? '•',
        maxLength: widget.maxLength,
        onChanged: widget.onChanged ?? (value) {},
        onFieldSubmitted: widget.onFieldSubmitted,
        inputFormatters: widget.inputFormatters,
        decoration:
            widget.inputDecoration ??
            InputDecoration(
              counterText: '',
              hintStyle:
                  widget.hintStyle ??
                  Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontSize:
                        widget.textStyleFontSize ??
                        ResponsiveHelper.getFontSize(context, mobile: 16, tablet: 16, desktop: 16),
                    color: Theme.of(context).customColors.textdarkcolor,
                    fontWeight: FontWeight.w400,
                  ),
              hintText: widget.hintLabel,
              label:
                  widget.label != null
                      ? Text(
                        widget.label!,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize:
                              widget.textStyleFontSize ??
                              ResponsiveHelper.getFontSize(context, mobile: 16, tablet: 16, desktop: 16),
                          color: Theme.of(context).customColors.secondaryTextColor,
                          fontWeight: FontWeight.w400,
                        ),
                      )
                      : null,
              contentPadding: widget.contentPadding ?? const EdgeInsets.symmetric(vertical: 12.0, horizontal: 20.0),
              prefixIcon:
                  widget.prefixIcon != null
                      ? SizedBox(
                        height: ResponsiveHelper.getWidgetHeight(context, mobile: 22, tablet: 24, desktop: 26),
                        width: ResponsiveHelper.getWidgetSize(context, mobile: 22, tablet: 24, desktop: 26),
                        child: widget.prefixIcon,
                      )
                      : null,
              prefixIconConstraints: widget.boxConstraints,
              suffixIconConstraints: widget.suffixBoxConstraints,
              fillColor: widget.fillColor ?? Theme.of(context).customColors.fillColor,
              filled: true,
              errorMaxLines: 2,
              suffixIcon:
                  widget.suffixText == true
                      ? widget.suffixIcon
                      : (widget.suffixIcon != null
                          ? SizedBox(
                            height: ResponsiveHelper.getWidgetHeight(context, mobile: 22, tablet: 24, desktop: 26),
                            width: ResponsiveHelper.getWidgetSize(context, mobile: 22, tablet: 24, desktop: 26),
                            child: widget.suffixIcon,
                          )
                          : null),
              isDense: widget.isDense ?? false,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.0),
                borderSide: BorderSide(
                  color:
                      _isHovered
                          ? Theme.of(context).customColors.hoverBorderColor!
                          : Theme.of(context).customColors.borderColor!,
                  width: 1.5,
                ),
              ),
              enabledBorder:
                  _isHovered
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(12.0)),
                        borderSide: BorderSide(color: Theme.of(context).customColors.hoverBorderColor!, width: 1.5),
                      )
                      : OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.0),
                        borderSide: BorderSide(color: Theme.of(context).customColors.borderColor!, width: 1.5),
                      ),
              focusedBorder:
                  _isHovered
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(12.0)),
                        borderSide: BorderSide(color: Theme.of(context).customColors.hoverBorderColor!, width: 1.5),
                      )
                      : OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.0),
                        borderSide: BorderSide(color: Theme.of(context).customColors.primaryColor!, width: 1.5),
                      ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.0),
                borderSide: BorderSide(color: Theme.of(context).customColors.redColor!, width: 1.5),
              ),
            ),
        contextMenuBuilder: widget.contextMenuBuilder ?? _defaultContextMenuBuilder,
        autovalidateMode: widget.autovalidateMode,
        errorBuilder: (context, errorText) {
          final resolvedPadding = (widget.contentPadding ?? const EdgeInsets.symmetric(horizontal: 20.0)).resolve(
            Directionality.of(context),
          );
          return Container(
            transform: Matrix4.translationValues(-resolvedPadding.left, 0.0, 0.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              spacing: 8.0,
              children: [
                Icon(Icons.error_outline, color: Theme.of(context).customColors.errorColor, size: 18.0),
                Expanded(
                  child: Text(
                    errorText,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontSize:
                          widget.textStyleFontSize ??
                          ResponsiveHelper.getFontSize(context, mobile: 16, tablet: 16, desktop: 16),
                      fontWeight: FontWeight.w400,
                      color: Theme.of(context).customColors.errorColor,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _defaultContextMenuBuilder(BuildContext context, EditableTextState editableTextState) {
    if (defaultTargetPlatform == TargetPlatform.iOS && SystemContextMenu.isSupported(context)) {
      return SystemContextMenu.editableText(editableTextState: editableTextState);
    }
    return AdaptiveTextSelectionToolbar.editableText(editableTextState: editableTextState);
  }

  TextInputType _getKeyboardType() {
    switch (widget.type) {
      case InputType.email:
        return TextInputType.emailAddress;
      case InputType.phoneNumber:
        return TextInputType.phone;
      case InputType.digits:
        return TextInputType.number;
      case InputType.decimalDigits:
        return const TextInputType.numberWithOptions(decimal: true);
      case InputType.multiline:
        return TextInputType.multiline;
      default:
        return TextInputType.text;
    }
  }
}
