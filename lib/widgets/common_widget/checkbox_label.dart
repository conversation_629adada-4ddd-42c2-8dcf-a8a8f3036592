import 'package:exchek/core/utils/exports.dart';

class CustomCheckBoxLabel extends StatelessWidget {
  final bool isSelected;
  final String label;
  final VoidCallback onChanged;

  const CustomCheckBoxLabel({super.key, required this.isSelected, required this.label, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onChanged,
      child: Row(
        children: [
          isSelected
              ? Container(
                height: 18.0,
                width: 18.0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.0),
                  color: Theme.of(context).customColors.primaryColor,
                ),
                child: Icon(Icons.done, color: Theme.of(context).customColors.fillColor, size: 16.0),
              )
              : Container(
                height: 18.0,
                width: 18.0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.0),
                  border: Border.all(color: Theme.of(context).customColors.dividerColor!),
                ),
              ),
          const SizedBox(width: 4.0),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: ResponsiveHelper.getFontSize(context, mobile: 14.0, tablet: 14.0, desktop: 16.0),
                fontWeight: FontWeight.w400,
                letterSpacing: 0.16,
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
