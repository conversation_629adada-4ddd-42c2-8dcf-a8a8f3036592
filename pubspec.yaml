name: exchek
description: "Exchek is a comprehensive B2B and B2C financial application designed to simplify cross-border transactions and currency exchange."
publish_to: "none"
version: 1.0.1+3

environment:
  sdk: ^3.7.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  flutter_bloc: ^9.1.1
  dio: ^5.8.0+1
  cupertino_icons: ^1.0.8
  connectivity_plus: ^6.1.3
  flutter_secure_storage: ^10.0.0-beta.4
  shared_preferences: ^2.2.2
  encrypt: ^5.0.1
  google_fonts: ^6.2.1
  lottie: ^3.3.1
  flutter_svg: ^2.0.17
  uuid: ^4.5.1
  cached_network_image: ^3.4.1
  flutter_cache_manager: ^3.4.1
  shimmer: ^3.0.0
  equatable: ^2.0.7
  toastification: ^2.3.0
  flutter_native_splash: ^2.4.6
  pretty_dio_logger: ^1.4.0
  flutter_dotenv: ^5.2.1
  device_info_plus: ^9.1.2
  permission_handler: ^12.0.0+1
  intl: ^0.20.2
  keyboard_actions: ^4.2.0
  go_router: ^14.2.0
  package_info_plus: ^8.3.0
  wave: ^0.2.2
  dynamic_path_url_strategy: ^1.0.0
  file_picker: ^10.1.9
  flutter_spinkit: ^5.2.1
  image_picker: ^1.1.2
  dotted_border: ^3.0.1
  flutter_widget_from_html: ^0.16.0
  oauth2_client: ^4.2.0
  country_picker: ^2.0.27
  dropdown_textfield: ^1.2.0
  camera: ^0.11.1
  http_parser: ^4.1.2
  cron: ^0.6.2
  flutter_dropzone: ^4.2.1
  pdfx: ^2.9.2
  
# Code Generation
flutter_gen:
  line_length: 160
  integrations:
    flutter_svg: true
    lottie: true
  assets:
    enabled: true
  output: lib/core/generated

dev_dependencies:
  flutter_test:
    sdk: flutter
    # Code Generation
  mockito: ^5.4.6
  mocktail: ^1.0.4
  build_runner: ^2.4.15
  bloc_test: ^10.0.0
  flutter_lints: ^5.0.0
  intl_utils: ^2.8.7
  flutter_gen_runner: ^5.8.0


flutter_intl:
  enabled: true
  class_name: Lang
  main_locale: en
  arb_dir: lib/core/l10n 
  output_dir: lib/core/generated

flutter:
  uses-material-design: true
  assets:
    - .env
    - assets/images/pngs/
    - assets/images/svgs/
    - assets/images/svgs/icons/
    - assets/images/svgs/other/
    - assets/images/pngs/authentication/
    - assets/terms_and_condition/
    - assets/images/svgs/country/

  
         
flutter_native_splash:
  color: "#FFFFFF"
  android: true 
  ios: true   
  web: false  
